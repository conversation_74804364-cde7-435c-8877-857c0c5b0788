# 领慧助手 - 产品使用文档

## 概述

**领慧助手** (<PERSON> Hui Assistant) 是一款基于 Electron 构建的桌面 AI 助手应用，集成多种 AI 模型（OpenAI、Anthropic Claude、DeepSeek），并通过 MCP（模型上下文协议）扩展工具生态系统。应用提供智能对话、文档处理、工具集成等功能，为用户打造全方位的 AI 工作助手。

## 系统要求

### 支持平台

- **Windows**: Windows 10 及以上（x64）
- **macOS**: macOS 10.15 及以上（Intel/Apple Silicon）

### 网络要求

- 稳定的网络连接（用于 AI 模型 API 调用）
- 支持 HTTPS 协议

## 安装与启动

### 初次安装

1. 下载适合您操作系统的安装包
2. 运行安装程序，按提示完成安装
3. 启动应用程序

### 登录认证

1. 应用启动后，系统将显示二维码登录界面
2. 使用"丰声"手机应用扫描二维码
3. 二维码每 30 秒自动刷新，确保安全性
4. 扫码成功后，自动跳转到主界面

## 主要功能模块

### 1. 会话（Chat Studio）

**会话模块是应用的核心功能，提供智能对话服务。**

#### 1.1 创建新会话

- 点击左侧导航栏的"会话"进入聊天界面
- 点击"新建会话"按钮创建对话
- 可自定义会话名称，便于管理

#### 1.2 模型选择与配置

- **模型选择**: 从已启用的 AI 模型中选择合适的模型
- **高级配置**:
  - **系统提示词**: 自定义 AI 助手的行为和角色
  - **温度 (0-2)**: 控制回答的创造性，数值越高越有创意
  - **最大标记数 (0-8192)**: 限制单次回答的长度
  - **Top-P (0-1)**: 控制回答的多样性
  - **Top-K (0-100)**: 限制候选词汇数量
  - **随机种子**: 设置回答的随机性
  - **重复惩罚 (-2 到 2)**: 避免重复内容

#### 1.3 对话交互

- **文本对话**: 支持实时流式响应
- **文件上传**:
  - 支持拖拽上传文件
  - 文档预览功能
  - 支持格式：Excel、Word、PDF、PowerPoint 等
- **多媒体支持**:
  - 图片上传与分析
  - 图表生成（ECharts）
  - 流程图绘制（Mermaid）

#### 1.4 MCP 工具集成

- 在会话设置中启用所需的 MCP 工具
- 工具会自动集成到对话中，提供扩展功能
- 支持文件系统操作、文档处理等工具

#### 1.5 会话管理

- **重命名会话**: 双击会话名称进行编辑
- **删除会话**: 右键菜单删除不需要的会话
- **会话历史**: 自动保存对话记录
- **消息操作**: 复制消息、查看原始内容

### 2. 模型管理（Model Service）

**配置和管理 AI 模型提供商。**

#### 2.1 提供商配置

支持以下 AI 模型提供商：

- **OpenAI**: GPT 系列模型
- **Anthropic**: Claude 系列模型
- **DeepSeek**: 专业推理和编程模型

#### 2.2 API 配置

- **基础 URL**: 设置自定义 API 端点
- **API 密钥**: 安全存储认证信息
- **连接测试**: 验证 API 配置是否正确

#### 2.3 模型目录

- **模型列表**: 查看所有可用模型
- **功能标签**:
  - 🔧 函数调用支持
  - 🧠 推理模型
  - 🖼️ 多模态支持
  - 📚 超长上下文
- **启用/禁用**: 灵活控制可用模型

#### 2.4 默认设置

- 设置默认使用的模型
- 配置默认参数值

### 3. MCP 服务（MCP Servers）

**管理外部工具和服务集成。**

#### 3.1 服务发现

- **MCP 市场**: 浏览可用的 MCP 服务器
- **分类筛选**: 按功能分类查找工具
- **搜索功能**: 快速定位所需工具

#### 3.2 服务器类型

- **STDIO 服务器**: 本地进程型服务器
- **SSE 服务器**: 服务器推送事件型
- **StreamableHTTP**: RESTful HTTP 流服务器

#### 3.3 内置工具

应用预装以下核心工具：

- **文件系统工具**:
  - 目录浏览和导航
  - 文件读写操作
  - 文件搜索功能
  - 安全路径验证
- **文档处理工具**:
  - Excel 文件解析（.xlsx, .xlsm, .xlsb, .xls）
  - Word 文档处理（.doc, .docx）
  - PDF 文件解析
  - PowerPoint 演示文稿处理（.ppt, .pptx）

#### 3.4 自定义服务器

- **添加服务器**: 配置自定义 MCP 服务器
- **配置管理**:
  - 表单配置（简化模式）
  - JSON 编辑器（高级模式）
  - 环境变量设置
  - 命令行参数配置
- **状态监控**: 实时显示服务器运行状态
- **工具管理**: 单独启用/禁用特定工具

#### 3.5 服务器配置示例

**添加本地 Python MCP 服务器：**

```json
{
  "command": "python",
  "args": ["/path/to/your/mcp_server.py"],
  "env": {
    "PYTHONPATH": "/path/to/dependencies"
  }
}
```

**添加 Node.js MCP 服务器：**

```json
{
  "command": "node",
  "args": ["/path/to/server.js"],
  "env": {
    "NODE_ENV": "production"
  }
}
```

### 4. 设置（Settings）

**应用程序配置和系统管理。**

#### 4.1 环境配置

- **依赖管理**:
  - MSET（MCP 服务器管理工具）
  - Node.js 运行时
  - UV（Python 包管理器）
  - Bun（JavaScript 运行时）
- **自动检测**: 系统自动检测已安装的依赖
- **一键安装**: 简化依赖安装流程
- **状态监控**: 可视化显示组件状态

#### 4.2 关于和更新

- **版本信息**: 显示当前应用版本
- **更新检查**: 自动检查新版本
- **更新下载**: 显示下载进度和速度
- **版本历史**: 查看更新日志（如有）

#### 4.3 用户管理

- **用户信息**: 显示登录用户信息
- **安全退出**: 安全注销登录状态

## 高级功能

### 1. 协议集成

- **自定义协议**: 支持 `alink://` 协议唤起应用
- **深度链接**: 通过 URL 直接导航到特定功能
- **外部集成**: 与其他应用程序无缝集成

### 2. 文件处理能力

#### 2.1 支持的文件格式

- **Office 文档**: .docx, .doc, .xlsx, .xlsm, .xlsb, .xls, .pptx, .ppt
- **PDF 文档**: 文本提取、表格解析、元数据提取
- **图片文件**: 多模态模型支持的图片格式
- **文本文件**: .txt, .md, .json, .csv 等

#### 2.2 处理功能

- **内容提取**: 自动提取文档文本内容
- **结构解析**: 保持文档结构和格式
- **元数据提取**: 获取文件属性和元信息
- **格式转换**: 支持多种输出格式

### 3. 企业级功能

- **多环境支持**: 生产、测试、开发环境配置
- **自动更新**: 内置更新机制
- **安全性**:
  - 本地数据存储
  - API 密钥加密
  - 安全的 IPC 通信
  - 文件系统访问控制

## 最佳实践

### 1. 模型选择建议

- **日常对话**: 使用 GPT-3.5 或 Claude-3-haiku（速度快，成本低）
- **专业分析**: 使用 GPT-4 或 Claude-3-opus（质量高）
- **代码编程**: 使用 DeepSeek-Coder 或 GPT-4（专业编程能力）
- **长文档处理**: 选择支持超长上下文的模型

### 2. 工具配置建议

- **初学者**: 使用内置工具即可满足基本需求
- **进阶用户**: 根据工作流程添加专业 MCP 工具
- **开发者**: 自定义 MCP 服务器集成特定功能

### 3. 性能优化

- **会话管理**: 定期清理不需要的历史会话
- **文件上传**: 大文件建议先压缩或分割
- **模型参数**: 根据任务需求调整参数，避免过度配置

### 4. 安全建议

- **API 密钥**: 定期更换和检查 API 密钥安全性
- **文件上传**: 不上传包含敏感信息的文件
- **网络环境**: 在安全的网络环境中使用应用

## 故障排除

### 常见问题

#### 1. 登录问题

**问题**: 二维码扫描失败
**解决方案**:

- 检查网络连接
- 确保使用最新版本的"丰声"应用
- 等待二维码自动刷新后重试

#### 2. 模型连接问题

**问题**: API 调用失败
**解决方案**:

- 验证 API 密钥是否正确
- 检查网络连接和防火墙设置
- 确认 API 额度和权限
- 尝试重置基础 URL 为默认值

#### 3. MCP 服务器问题

**问题**: 工具无法使用
**解决方案**:

- 检查服务器运行状态
- 验证配置参数是否正确
- 查看错误日志
- 重启相关服务器

#### 4. 文件上传问题

**问题**: 文件处理失败
**解决方案**:

- 确认文件格式是否支持
- 检查文件大小限制
- 验证文件是否损坏
- 尝试转换文件格式

#### 5. 性能问题

**问题**: 应用运行缓慢
**解决方案**:

- 关闭不必要的会话
- 清理应用缓存
- 检查系统资源使用情况
- 重启应用程序

### 获取帮助

- **应用内帮助**: 点击左侧导航栏的帮助中心
- **在线文档**: 访问官方文档网站
- **技术支持**: 联系企业技术支持团队

## 更新说明

### 自动更新

- 应用会自动检查新版本
- 更新通知会显示在导航栏
- 支持后台下载和安装

### 手动更新

1. 进入"设置" → "关于"
2. 点击"检查更新"按钮
3. 按提示下载并安装新版本

## 隐私和安全

### 数据存储

- **本地存储**: 所有聊天记录和配置存储在本地
- **加密保护**: API 密钥等敏感信息加密存储
- **无云同步**: 应用不会将数据上传到云端

### 网络通信

- **HTTPS**: 所有网络通信使用加密协议
- **API 调用**: 仅向配置的 AI 服务提供商发送数据
- **无第三方**: 不向未授权的第三方传输数据

### 权限管理

- **文件访问**: 仅访问用户主动选择的文件
- **网络权限**: 仅用于 AI API 调用和应用更新
- **系统权限**: 最小化权限原则

---

**版本**: V1.3  
**更新日期**: 2024 年  
**适用平台**: Windows 10+, macOS 10.15+

如需更多帮助或技术支持，请联系您的系统管理员或查看应用内帮助中心。
