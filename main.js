const { app, ip<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } = require('electron');
const path = require('path');
const { URL } = require('url');
const env = require('dotenv').config({
  path: path.join(__dirname, '.env')
});

process.env.APP_ENV = env.parsed.APP_ENV;
process.env.APP_VERSION = env.parsed.APP_VERSION;
process.env.BASE_SERVER_MARKET_URL = env.parsed.BASE_SERVER_MARKET_URL;
process.env.CAS_URL = env.parsed.CAS_URL;
process.env.CAS_SERVICE_ID = env.parsed.CAS_SERVICE_ID;
process.env.TJ_URL = env.parsed.TJ_URL;
process.env.TJ_APP_KEY = env.parsed.TJ_APP_KEY;
process.env.TJ_APP_SECRET = env.parsed.TJ_APP_SECRET;
process.env.STUDIO_NAME = env.parsed.STUDIO_NAME;

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// 全局变量存储待处理的URL
global.pendingUrl = null;

const isDevelopment = process.env.NODE_ENV === 'development';


let win;
let tray = null;

const startWithProtocol = env.parsed.APP_ENV === 'SIT' ? 'ailinksit://' : 'alink://';
const protocol = env.parsed.APP_ENV === 'SIT' ? 'ailinksit' : 'alink';

// 设置默认协议客户端
if (!app.isDefaultProtocolClient(protocol)) {
  app.setAsDefaultProtocolClient(protocol);
}

// alink://mcp?data=base64encodeddata



/**
 * 处理协议URL
 * @param {string[]} commandLine - 命令行参数数组
 */
function handleProtocolUrl(commandLine) {
  // 查找以alink://开头的URL
  const protocolUrl = commandLine.find(arg => arg.startsWith(startWithProtocol));
  if (!protocolUrl) {
    return;
  }

  console.log('收到协议URL:', protocolUrl);

  // 直接存储完整URL，不做任何解析和处理
  global.pendingUrl = protocolUrl;

  // 通知渲染进程有新的URL
  if (win && win.webContents) {
    win.webContents.send('mcp-url-received', {
      hasPendingUrl: true,
      url: protocolUrl
    });
  }
}

// 确保应用程序只有一个实例
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  // 如果无法获得锁，说明另一个实例已经在运行，应该退出
  app.quit();
} else {
  // 当尝试运行第二个实例时，将激活已经运行的实例窗口
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // 处理协议URL
    handleProtocolUrl(commandLine);

    if (win) {
      // 如果窗口存在但最小化了，则恢复窗口
      if (win.isMinimized()) win.restore();
      // 激活窗口，使其成为焦点
      win.focus();
    }
  });

  const createTray = () => {
    // 根据平台选择合适的图标
    const iconPath = process.platform === 'darwin'
      ? path.join(__dirname, 'build/icons/tray.png')  // macOS 使用 PNG
      : path.join(__dirname, 'build/icons/icon.ico');  // Windows 使用 ICO

    tray = new Tray(iconPath);

    // 设置托盘提示文本
    tray.setToolTip('领慧助手');

    // 创建托盘右键菜单
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          if (win) {
            if (win.isMinimized()) win.restore();
            win.show();
            win.focus();
          } else {
            createWindow();
          }
        }
      },
      {
        label: '隐藏窗口',
        click: () => {
          if (win) {
            win.hide();
          }
        }
      },
      { type: 'separator' },
      {
        label: '退出应用',
        click: () => {
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);

    // 托盘图标点击事件
    tray.on('click', () => {
      if (win) {
        win.show();
        win.focus();
      } else {
        createWindow();
      }
    });

    // macOS 双击事件
    if (process.platform === 'darwin') {
      tray.on('double-click', () => {
        if (win) {
          win.show();
          win.focus();
        } else {
          createWindow();
        }
      });
    }
  };

  const createWindow = () => {
    // console.log(path.join(__dirname, 'preload.js'));

    const size =
      process.platform === 'darwin'
        ? {
          width: 1280,
          height: 720,
          minWidth: 1080,
          minHeight: 600,
        }
        : {
          width: 1080,
          height: 600,
          minWidth: 800,
          minHeight: 600,
        };

    win = new BrowserWindow({
      ...size,
      webPreferences: {
        contextIsolation: true,
        nodeIntegration: true,
        preload: path.join(__dirname, 'preload.js'),
      },
      frame: false,
      // 允许关闭在左上角
      titleBarStyle: 'hidden',
      // 添加这个选项，让窗口关闭时隐藏到托盘而不是退出
      show: false,
    });

    if (isDevelopment) {
      win.loadURL('http://localhost:8088');
    } else {
      win.loadFile(path.join(__dirname, 'dist/index.html'));
    }

    // 解决应用启动白屏问题
    win.on('ready-to-show', () => {
      win.show();
      win.focus();
      // 设置剪贴板处理器的主窗口
      // clipboardImageHandler.setMainWindow(win);
    });

    // 修改窗口关闭行为，隐藏到托盘而不是退出
    win.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault();
        win.hide();
        // 在 Windows 上显示托盘通知
        if (process.platform === 'win32' && tray) {
          // tray.displayBalloon({
          //   iconType: 'info',
          //   title: '领慧助手',
          //   content: '应用已最小化到系统托盘'
          // });
        }
      }
    });

    // 当窗口关闭时发出。在你收到这个事件后，你应该删除对窗口的引用，并避免再使用它。
    win.on('closed', () => {
      win = null;
    });

    if (isDevelopment) {
      win.webContents.openDevTools();
    }
    // 打开开发者工具
  };

  const createApplicationMenu = () => {
    const template = [
      // macOS 应用菜单
      {
        label: app.getName(),
        submenu: [
          {
            label: '关于 ' + app.getName(),
            role: 'about'
          },
          { type: 'separator' },
          // {
          //   label: '偏好设置...',
          //   accelerator: 'CmdOrCtrl+,',
          //   click: () => {
          //     // 这里可以添加打开设置页面的逻辑
          //     if (win) {
          //       win.webContents.send('open-preferences');
          //     }
          //   }
          // },
          // { type: 'separator' },
          {
            label: '服务',
            role: 'services',
            submenu: []
          },
          { type: 'separator' },
          {
            label: '隐藏 ' + app.getName(),
            accelerator: 'Command+H',
            role: 'hide'
          },
          {
            label: '隐藏其他',
            accelerator: 'Command+Shift+H',
            role: 'hideothers'
          },
          {
            label: '显示全部',
            role: 'unhide'
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: 'Command+Q',
            click: () => {
              app.isQuiting = true;
              app.quit();
            }
          }
        ]
      },
      // 文件菜单
      // {
      //   label: '文件',
      //   submenu: [
      //     {
      //       label: '新建对话',
      //       accelerator: 'CmdOrCtrl+N',
      //       click: () => {
      //         if (win) {
      //           win.webContents.send('new-chat');
      //         }
      //       }
      //     },
      //     { type: 'separator' },
      //     {
      //       label: '导入配置',
      //       accelerator: 'CmdOrCtrl+O',
      //       click: () => {
      //         if (win) {
      //           win.webContents.send('import-config');
      //         }
      //       }
      //     },
      //     {
      //       label: '导出配置',
      //       accelerator: 'CmdOrCtrl+S',
      //       click: () => {
      //         if (win) {
      //           win.webContents.send('export-config');
      //         }
      //       }
      //     }
      //   ]
      // },
      // 编辑菜单
      {
        label: '编辑',
        submenu: [
          {
            label: '撤销',
            accelerator: 'CmdOrCtrl+Z',
            role: 'undo'
          },
          {
            label: '重做',
            accelerator: 'Shift+CmdOrCtrl+Z',
            role: 'redo'
          },
          { type: 'separator' },
          {
            label: '剪切',
            accelerator: 'CmdOrCtrl+X',
            role: 'cut'
          },
          {
            label: '复制',
            accelerator: 'CmdOrCtrl+C',
            role: 'copy'
          },
          {
            label: '粘贴',
            accelerator: 'CmdOrCtrl+V',
            role: 'paste'
          },
          {
            label: '全选',
            accelerator: 'CmdOrCtrl+A',
            role: 'selectall'
          }
        ]
      },
      // 视图菜单
      {
        label: '视图',
        submenu: [
          {
            label: '重新加载',
            accelerator: 'CmdOrCtrl+R',
            click: (item, focusedWindow) => {
              if (focusedWindow) focusedWindow.reload();
            }
          },
          {
            label: '强制重新加载',
            accelerator: 'CmdOrCtrl+Shift+R',
            click: (item, focusedWindow) => {
              if (focusedWindow) focusedWindow.webContents.reloadIgnoringCache();
            }
          },
          {
            label: '切换开发者工具',
            accelerator: process.platform === 'darwin' ? 'Alt+Command+I' : 'Ctrl+Shift+I',
            click: (item, focusedWindow) => {
              if (focusedWindow) focusedWindow.webContents.toggleDevTools();
            }
          },
          { type: 'separator' },
          {
            label: '实际大小',
            accelerator: 'CmdOrCtrl+0',
            click: (item, focusedWindow) => {
              if (focusedWindow) focusedWindow.webContents.zoomLevel = 0;
            }
          },
          {
            label: '放大',
            accelerator: 'CmdOrCtrl+Plus',
            click: (item, focusedWindow) => {
              if (focusedWindow) {
                const currentZoom = focusedWindow.webContents.zoomLevel;
                focusedWindow.webContents.zoomLevel = currentZoom + 0.5;
              }
            }
          },
          {
            label: '缩小',
            accelerator: 'CmdOrCtrl+-',
            click: (item, focusedWindow) => {
              if (focusedWindow) {
                const currentZoom = focusedWindow.webContents.zoomLevel;
                focusedWindow.webContents.zoomLevel = currentZoom - 0.5;
              }
            }
          },
          { type: 'separator' },
          {
            label: '切换全屏',
            accelerator: process.platform === 'darwin' ? 'Ctrl+Command+F' : 'F11',
            click: (item, focusedWindow) => {
              if (focusedWindow) {
                focusedWindow.setFullScreen(!focusedWindow.isFullScreen());
              }
            }
          }
        ]
      }
    ];

    // 在非 macOS 平台上移除第一个应用菜单
    if (process.platform !== 'darwin') {
      template.shift();
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  };

  app.whenReady().then(() => {
    // 远程加载 本地开发

    const { registerAPI } = require('./clients/node/common-api');
    const { registerSa } = require('./clients/node/sa/sa');
    const { initUserDB } = require('./clients/node/db/init');
    registerSa();
    registerAPI();
    createApplicationMenu(); // 创建应用菜单
    createWindow();
    createTray(); // 创建托盘图标
    initUserDB();

    // 处理启动时的协议URL
    handleProtocolUrl(process.argv);

    // 初始化自动更新

    app.on('activate', () => {
      // macOS Dock 图标点击事件处理
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
      } else if (win) {
        // 如果窗口存在但被隐藏，则显示并聚焦
        if (!win.isVisible()) {
          win.show();
        }
        if (win.isMinimized()) {
          win.restore();
        }
        win.focus();
      }
    });
  });

  app.on('window-all-closed', () => {
    // 修改退出逻辑，保持应用在后台运行
    if (process.platform !== 'darwin') {
      // 在非 macOS 平台上，如果不是主动退出，则不退出应用
      if (app.isQuiting) {
        app.quit();
      }
    }
  });

  app.on('open-url', (event, url) => {
    event.preventDefault();
    if (win) {
      if (win.isMinimized()) win.restore();
      win.focus();
    }

    // 处理alink协议
    if (url.startsWith(startWithProtocol)) {
      handleProtocolUrl([url]);
      return;
    }
  });
}

ipcMain.on('window-minimize', () => {
  win.minimize();
});

ipcMain.on('window-maximize', () => {
  if (win.isMaximized()) {
    win.unmaximize();
  } else {
    win.maximize();
  }
});

ipcMain.handle('toggle-fullscreen', (event) => {
  const currentWin = BrowserWindow.fromWebContents(event.sender);
  if (currentWin && currentWin.id === win.id) {
    currentWin.setFullScreen(!currentWin.isFullScreen());
  }
});

ipcMain.on('window-close', () => {
  win.close();
});

ipcMain.handle('init', () => {
  const { initDB } = require('./clients/node/db/init');
  initDB();
  const { ElectronAgent } = require('./clients/node/chat/ElectronAgent');
  setTimeout(() => {
    ElectronAgent.getInstance().init();
  }, 1000);
  return true;
});

// 获取待处理的URL
ipcMain.handle('get-pending-url', () => {
  return global.pendingUrl;
});

// 清空待处理的URL
ipcMain.handle('clear-pending-url', () => {
  global.pendingUrl = null;
  return true;
});

app.on('before-quit', () => {
  app.isQuiting = true;
  try {
    const { deleteUserFromDb, getUserFromDb } = require('./clients/node/db/user');
    const { ElectronAgent } = require('./clients/node/chat/ElectronAgent');
    ElectronAgent.getInstance().close();
    ElectronAgent.destroyInstance();
    deleteUserFromDb();
  } catch (e) {
    console.error(e);
  }
  // 销毁托盘图标
  if (tray) {
    tray.destroy();
  }
});
