import { ipc<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron';
import { Session } from '../../node/db/sessions';
import { Message, AttachmentInfo } from '../../node/db/messages';

// 为不同类型的监听器创建独立的Map
let messageFnMap = new Map<string, (event: IpcRendererEvent, data: any) => void>();
let clearContextFnMap = new Map<string, (event: IpcRendererEvent, data: any) => void>();
let interruptStreamFnMap = new Map<string, (event: IpcRendererEvent, data: any) => void>();

export const chat = async (prompt: string, sessionId: number, mcpIds: string, answerMessageId?: number, questionMessageId?: number, attachments?: AttachmentInfo[]): Promise<Message> => {
  const result = await ipcRenderer.invoke('chat', {
    prompt,
    sessionId,
    mcpIds,
    answerMessageId,
    questionMessageId,
    attachments
  });
  return result;
};

export const createSession = async (userId: string, provider?: string) => {
  const result = await ipcRenderer.invoke('create-session', {
    userId,
    provider
  });
  return result;
};

export const getSessionList = async (userId: string) => {
  const result = await ipcRenderer.invoke('get-session-list', {
    userId
  });
  return result;
};

export const deleteSession = async (id: number) => {
  const result = await ipcRenderer.invoke('delete-session', {
    id
  });
  return result;
};

export const updateSession = async (id: number, updates: Partial<Session>) => {
  const result = await ipcRenderer.invoke('update-session', {
    id,
    updates
  });
  return result;
};

export const getMessageList = async (sessionId: number) => {
  const result = await ipcRenderer.invoke('get-message-list', {
    sessionId
  });
  return result;
};

export const deleteMessage = async (id: number): Promise<Message> => {
  const result = await ipcRenderer.invoke('delete-message', {
    id
  });
  return result;
};

export const onMessage = async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
  // 如果已经存在该key的监听器，先移除
  const existingFn = messageFnMap.get(key);
  if (existingFn) {
    ipcRenderer.off('stream_message', existingFn);
  }

  const fn = (event: IpcRendererEvent, data: any) => {
    // console.log('onMessage', event, data);
    callback(event, data);
  }

  // 保存函数引用到Map中
  messageFnMap.set(key, fn);
  ipcRenderer.on('stream_message', fn);
}

export const onClearContext = async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
  const fn = (event: IpcRendererEvent, data: any) => {
    // console.log('onClearContext', event, data);
    callback(event, data);
  }
  clearContextFnMap.set(key, fn);
  ipcRenderer.on('clear-context', fn);
}

export const removeClearContextListener = async (key: string) => {
  const fn = clearContextFnMap.get(key);
  if (fn) {
    ipcRenderer.off('clear-context', fn);
    clearContextFnMap.delete(key);
  }
}

export const sendInterruptStream = async (sessionId: number) => {
  ipcRenderer.send('interrupt_stream', {
    sessionId
  });
}

export const onInterruptStream = async (key: string, callback: (event: IpcRendererEvent, data: any) => void) => {
  const fn = (event: IpcRendererEvent, data: any) => {
    // console.log('onInterruptStream', event, data);
    callback(event, data);
  }
  interruptStreamFnMap.set(key, fn);
  ipcRenderer.on('interrupt_stream', fn);
}

export const removeInterruptStreamListener = async (key: string) => {
  const fn = interruptStreamFnMap.get(key);
  if (fn) {
    ipcRenderer.off('interrupt_stream', fn);
    interruptStreamFnMap.delete(key);
  }
}

export const removeMessageListener = async (key: string) => {
  const fn = messageFnMap.get(key);
  if (fn) {
    console.log('removeMessageListener', key);
    console.log('fnMap中的函数:', fn);
    console.log('fnMap size before remove:', messageFnMap.size);

    ipcRenderer.off('stream_message', fn);
    messageFnMap.delete(key);

    console.log('fnMap size after remove:', messageFnMap.size);
    console.log('监听器移除完成');
  } else {
    console.log('未找到key对应的监听器:', key);
    console.log('当前fnMap keys:', Array.from(messageFnMap.keys()));
  }
}

export const clearContext = async (sessionId: number) => {
  const result = await ipcRenderer.invoke('clear-context', {
    sessionId
  });
  return result;
}

export const deleteAllMessages = async (sessionId: number) => {
  const result = await ipcRenderer.invoke('delete-all-messages', {
    sessionId
  });
  return result;
}


export const summarizeMessageTitle = async (sessionId: number, provider: string, model: string) => {
  const result = await ipcRenderer.invoke('summarize-message-title', {
    sessionId,
    provider,
    model
  });
  return result;
}