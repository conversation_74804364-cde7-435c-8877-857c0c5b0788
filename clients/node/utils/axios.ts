import axios from 'axios';
import { BrowserWindow } from 'electron';
import logger from './logger';

const getMainWindow = () => {
  return BrowserWindow.getAllWindows()[0]
}


axios.interceptors.response.use((response) => {
  // logger.info('axios response', response);
  if (response.data.errorCode === '09020102') {
    getMainWindow().webContents.send('logout');
    return response;
  }
  return response;
}, (error) => {
  logger.error('axios error', error);

  // if (error.response.data.errorCode === '09020102') {
  //   getMainWindow().webContents.send('logout');
  //   return null;
  // }
  return Promise.reject(error);
});

export default axios;