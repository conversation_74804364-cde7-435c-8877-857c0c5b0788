import { BaseAgent } from './BaseAgent';
import { TaskAnalysis, TodoItem } from './types';
import { Model, Provider } from '../../db/provider';
import { Tool } from '@modelcontextprotocol/sdk/types';
import { OpenAI } from 'openai';
import { AgentController } from '../AgentController';

/**
 * PlanningAgent - Analyzes user intent and creates execution plans
 */
export class PlanningAgent extends BaseAgent {
  private llm: OpenAI | null = null;

  constructor(
    modelConfig: Model,
    providerConfig: Provider,
    tools: Tool[] = [],
    sessionId: number = 0,
    context: AgentController
  ) {
    super(modelConfig, providerConfig, tools, sessionId, context);
    this.initOpenAI();
  }

  /**
   * Initialize OpenAI client
   */
  private initOpenAI() {
    this.llm = new OpenAI({
      apiKey: this.providerConfig.currentAPIKey || this.providerConfig.defaultApiKey,
      baseURL: this.providerConfig.apiBaseUrl
    });
  }

  /**
   * Analyze user intent to determine if task is complex
   */
  async analyzeIntent(message: string): Promise<TaskAnalysis> {
    this.log('Analyzing user intent', { message: message.substring(0, 100) });

    if (!this.llm) {
      this.logError('OpenAI client not initialized');
      return this.fallbackAnalysis(message);
    }

    const analysisPrompt = `
分析以下用户请求，判断是否为复杂任务：

需要结合已有工具和历史对话内容，来判断用户的意图进行分析，

你拥有的工具: ${this.tools.map(tool => tool.name).join(', ')}

用户请求: "${message}"

请分析：
1. 任务复杂度 (1-5星)
2. 是否需要多步骤执行
3. 是否需要调用工具
4. 任务类型 (coding/analysis/research/general)
5. 置信度 (0-1)

仅返回JSON格式，严格遵守good case 的样例，不能返回md格式:
good case:
{
  "isComplexTask": boolean,
  "userIntent": "用户意图概述",
  "complexity": number,
  "estimatedSteps": number,
  "requiresTools": boolean,
  "taskType": "任务类型",
  "confidence": number
}
bad case:
\`\`\`json
{
  "isComplexTask": boolean,
  "userIntent": "用户意图概述",
  "complexity": number,
  "estimatedSteps": number,
  "requiresTools": boolean,
  "taskType": "任务类型",
  "confidence": number
}
\`\`\`
`;

    try {
      const response = await this.llm.chat.completions.create({
        model: this.modelConfig.model,
        messages: [
          {
            role: 'system',
            content: '你是一个任务分析专家，专门负责分析用户请求的复杂度和执行需求。'
          },
          ...this.getMessagesDefinition(this.context?.context.historyMessages || []),
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        temperature: this.modelConfig.temperature,
        max_tokens: this.modelConfig.max_tokens
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        this.logError('No response from OpenAI');
        return this.fallbackAnalysis(message);
      }

      try {
        const analysis = JSON.parse(content.trim());
        this.log('Intent analysis completed', analysis);
        return analysis;
      } catch (parseError) {
        this.logError('Failed to parse analysis response', parseError);
        return this.fallbackAnalysis(message);
      }
    } catch (error) {
      this.logError('Intent analysis failed', error);
      return this.fallbackAnalysis(message);
    }
  }


  /**
   * Create detailed execution plan based on analysis
   */
  async createPlan(message: string, analysis: TaskAnalysis): Promise<TodoItem[]> {
    this.log('Creating execution plan', { analysis });

    const planningPrompt = `
基于以下任务分析，创建详细的执行计划：

任务: "${message}"
复杂度: ${analysis.complexity}/5
类型: ${analysis.taskType}
需要工具: ${analysis.requiresTools}

可用工具: ${this.tools.map(tool =>
      `${tool.name}: ${tool.description}`
    ).join('\n')}

当前执行轮次的上下文: ${this.context?.context.assistantContext || ''}

请创建步骤清单，每个步骤指定：
1. 步骤描述
2. 执行方式 (chat/tool)
3. 如果是工具，指定工具名称
4. 预估时间（秒）

返回严格按照good case 的JSON格式:
good case:
[
  {
    "id": "step_1",
    "content": "步骤描述",
    "agent_type": "chat|tool",
    "tool_name": "工具名称(如果适用)",
    "estimated_time": 30
  }
]
bad case:
\`\`\`json
[
  {
    "id": "step_1",
    "content": "步骤描述",
    "agent_type": "chat|tool",
    "tool_name": "工具名称(如果适用)",
    "estimated_time": 30
  }
]
\`\`\`
`;

    if (!this.llm) {
      this.logError('OpenAI client not initialized');
      return this.generateBasicPlan(message, analysis);
    }

    try {
      // Use the contextual messages method from BaseAgent
      const messages = this.getContextualMessages(planningPrompt, false);

      const response = await this.llm.chat.completions.create({
        model: this.modelConfig.model,
        messages,
        temperature: 0.2,
        max_tokens: 1000
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        this.logError('No response from OpenAI for planning');
        return this.generateBasicPlan(message, analysis);
      }

      try {
        const plan = JSON.parse(content.trim());
        const todoItems = plan.map((step: any, index: number) => ({
          ...step,
          id: step.id || `step_${index + 1}`,
          status: 'pending' as const
        }));

        this.log('Execution plan created', { steps: todoItems.length });
        return todoItems;
      } catch (parseError) {
        this.logError('Failed to parse plan response', parseError);
        return this.generateBasicPlan(message, analysis);
      }
    } catch (error) {
      this.logError('Plan creation failed', error);
      return this.generateBasicPlan(message, analysis);
    }
  }

  /**
   * Fallback analysis when AI analysis fails
   */
  private fallbackAnalysis(message: string): TaskAnalysis {
    const complexKeywords = ['重构', '实现', '开发', '设计', '分析', '优化', '调试', '创建', '构建'];
    const isComplex = complexKeywords.some(keyword => message.includes(keyword));

    return {
      isComplexTask: isComplex || message.length > 100,
      userIntent: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
      complexity: isComplex ? 4 : 2,
      estimatedSteps: isComplex ? 5 : 2,
      requiresTools: this.tools.length > 0 && isComplex,
      taskType: 'general',
      confidence: 0.6
    };
  }

  /**
   * Generate basic plan when AI planning fails
   */
  private generateBasicPlan(_message: string, analysis: TaskAnalysis): TodoItem[] {
    const basicPlan: TodoItem[] = [
      {
        id: 'step_1',
        content: '理解和分析用户需求',
        status: 'pending',
        agent_type: 'chat',
        estimated_time: 30
      }
    ];

    if (analysis.requiresTools && this.tools.length > 0) {
      basicPlan.push({
        id: 'step_2',
        content: '调用相关工具执行任务',
        status: 'pending',
        agent_type: 'tool',
        tool_name: this.tools[0].name,
        estimated_time: 120
      });
    } else {
      basicPlan.push({
        id: 'step_2',
        content: '执行具体任务',
        status: 'pending',
        agent_type: 'chat',
        estimated_time: 120
      });
    }

    basicPlan.push({
      id: 'step_3',
      content: '总结和反馈结果',
      status: 'pending',
      agent_type: 'chat',
      estimated_time: 30
    });

    return basicPlan;
  }

  /**
   * Override system prompt for planning agent
   */
  protected getSystemPrompt(): string {
    return '你是一个任务规划专家，专门负责将复杂任务分解为可执行的步骤。';
  }
}
