import { BaseAgent } from './BaseAgent';
import { TodoItem } from './types';
import { Model, Provider } from '../../db/provider';
import { Tool } from '@modelcontextprotocol/sdk/types';
import { OpenAI } from 'openai';
import { AgentController } from '../AgentController';

/**
 * ChatAgent - Handles conversational tasks and simple operations with streaming support
 */
export class ChatAgent extends BaseAgent {
  private systemPrompt: string;
  private llm: OpenAI | null = null;
  private abortController: AbortController | null = null;

  constructor(
    modelConfig: Model,
    providerConfig: Provider,
    tools: Tool[],
    systemPrompt: string,
    sessionId: number = 0,
    context: AgentController
  ) {
    super(modelConfig, providerConfig, tools, sessionId, context);
    this.systemPrompt = systemPrompt;
    this.initOpenAI();
  }

  /**
   * Initialize OpenAI client
   */
  private initOpenAI() {
    this.llm = new OpenAI({
      apiKey: this.providerConfig.currentAPIKey || this.providerConfig.defaultApiKey,
      baseURL: this.providerConfig.apiBaseUrl
    });
  }

  /**
   * Process a specific step in the execution plan
   */
  async processStep(step: TodoItem): Promise<string> {
    this.log('Processing chat step', { stepId: step.id, content: step.content });

    const stepPrompt = `
## 当前执行步骤
步骤ID: ${step.id}
步骤内容: ${step.content}
步骤类型: ${step.agent_type}

## 执行要求
请根据上述历史对话和前序步骤结果，专注于执行当前步骤。
提供详细、准确的分析和解答，确保与前序步骤的连贯性。
`;

    try {
      return await this.streamChatWithContext(stepPrompt);
    } catch (error) {
      this.logError('Chat step failed', error);
      throw error;
    }
  }
  /**
   * Continue conversation with additional context
   */
  async continueConversation(
    message: string,
    previousResults: string[],
    sessionId: number
  ): Promise<string> {
    this.log('Continuing conversation', {
      message: message.substring(0, 100),
      previousResultsCount: previousResults.length
    });

    const contextPrompt = `
基于之前的执行结果，继续对话：

用户消息: ${message}

之前的执行结果:
${previousResults.map((result, index) => `${index + 1}. ${result}`).join('\n')}

请基于这些信息继续对话。
`;

    try {
      const content = await this.streamChat(contextPrompt);
      this.log('Conversation continued', { responseLength: content.length });
      return content;
    } catch (error) {
      this.logError('Conversation continuation failed', error);
      throw error;
    }
  }

  /**
   * Summarize execution results
   */
  async summarizeResults(
    originalPrompt: string,
    executionResults: string[],
    sessionId: number
  ): Promise<string> {
    this.log('Summarizing results', {
      originalPrompt: originalPrompt.substring(0, 100),
      resultsCount: executionResults.length
    });

    const summaryPrompt = `
请总结以下任务的执行结果：

原始任务: ${originalPrompt}

执行结果:
${executionResults.map((result, index) => `步骤 ${index + 1}: ${result}`).join('\n\n')}

请提供一个简洁的总结，包括：
1. 任务完成情况
2. 主要成果
3. 遇到的问题（如有）
4. 后续建议（如有）
`;

    try {
      const content = await this.streamChat(summaryPrompt);
      this.log('Results summarized', { summaryLength: content.length });
      return content;
    } catch (error) {
      this.logError('Results summarization failed', error);
      throw error;
    }
  }

  /**
   * Core streaming chat method using OpenAI with contextual messages
   */
  private async streamChatWithContext(prompt: string): Promise<string> {
    if (!this.llm) {
      throw new Error('OpenAI client not initialized');
    }

    // Create abort controller for this request
    this.abortController = new AbortController();

    // Use the new contextual messages method from BaseAgent
    const messages = this.getContextualMessages(prompt, true);

    try {
      const stream = await this.llm.chat.completions.create({
        model: this.modelConfig.model,
        messages,
        stream: true,
        temperature: 0.7,
        max_tokens: 4096
      }, {
        signal: this.abortController.signal
      });

      let content = '';

      for await (const chunk of stream) {
        if (this.abortController.signal.aborted) {
          break;
        }

        const delta = chunk.choices[0]?.delta?.content;
        if (delta) {
          content += delta;

          // Send streaming content to frontend
          this.context!.sendStreamMessage('message_delta_content_send', {
            sessionId: this.sessionId,
            content: delta
          });
        }
      }

      return content;
    } catch (error: any) {
      if (error.name === 'AbortError') {
        this.log('Chat stream aborted');
        throw new Error('Chat stream was aborted');
      }
      this.logError('Streaming chat failed', error);
      throw error;
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Core streaming chat method using OpenAI
   */
  private async streamChat(prompt: string): Promise<string> {
    if (!this.llm) {
      throw new Error('OpenAI client not initialized');
    }

    // Create abort controller for this request
    this.abortController = new AbortController();

    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      {
        role: 'user',
        content: prompt
      }
    ];

    try {
      const stream = await this.llm.chat.completions.create({
        model: this.modelConfig.model,
        messages,
        stream: true,
        temperature: 0.7,
        max_tokens: 4096
      }, {
        signal: this.abortController.signal
      });

      let content = '';

      for await (const chunk of stream) {
        if (this.abortController.signal.aborted) {
          break;
        }

        const delta = chunk.choices[0]?.delta?.content;
        if (delta) {
          content += delta;

          // Send streaming content to frontend
          this.context!.sendStreamMessage('message_delta_content_send', {
            sessionId: this.sessionId,
            content: delta
          });
        }
      }

      return content;
    } catch (error: any) {
      if (error.name === 'AbortError') {
        this.log('Chat stream aborted');
        throw new Error('Chat stream was aborted');
      }
      this.logError('Streaming chat failed', error);
      throw error;
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Interrupt current streaming
   */
  public interruptStream() {
    if (this.abortController) {
      this.abortController.abort();
      this.log('Chat stream interrupted');
    }
  }

  /**
   * Override system prompt to use the configured system prompt
   */
  protected getSystemPrompt(): string {
    return this.systemPrompt || '你是一个智能助手，专门负责协助用户完成各种任务。';
  }
}
