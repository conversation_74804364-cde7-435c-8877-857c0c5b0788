import { Model, Provider } from '../../db/provider';
import { Tool } from '@modelcontextprotocol/sdk/types';
import { BrowserWindow } from 'electron';
import logger from '../../utils/logger';
import { AttachmentInfo, Message } from '../../db/messages';
import { OpenAI } from 'openai';
import { isSupportFunctionCalling } from '../ModelAdapter';
import { AgentController } from '../AgentController';

/**
 * Base Agent class providing common functionality for all agent types
 */
export abstract class BaseAgent {
  protected modelConfig: Model;
  protected providerConfig: Provider;
  protected tools: Tool[];
  protected sessionId: number;
  protected context?: AgentController;

  constructor(
    modelConfig: Model,
    providerConfig: Provider,
    tools: Tool[] = [],
    sessionId: number = 0,
    context?: AgentController
  ) {
    this.modelConfig = modelConfig;
    this.providerConfig = providerConfig;
    this.tools = tools;
    this.sessionId = sessionId;
    this.context = context;
  }

  /**
   * Send stream message to frontend
   */
  protected sendStreamMessage(type: string, data: any) {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('stream_message', { type, data });
    }
  }

  /**
   * Stream content to frontend with typing effect
   */
  protected async streamContent(
    content: string, 
    sessionId: number, 
    type: string, 
    delay: number = 20
  ) {
    const chunks = content.match(/.{1,3}/g) || []; // Split into 3-character chunks
    
    for (const chunk of chunks) {
      this.sendStreamMessage(type, {
        sessionId,
        content: chunk
      });
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  /**
   * Get main window reference
   */
  protected getMainWindow(): BrowserWindow {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (!mainWindow) {
      throw new Error('Main window not found');
    }
    return mainWindow;
  }

  /**
   * Log agent activity
   */
  protected log(message: string, data?: any) {
    logger.info(`[${this.constructor.name}] ${message}`, data);
  }

  /**
   * Log agent errors
   */
  protected logError(message: string, error?: any) {
    logger.error(`[${this.constructor.name}] ${message}`, error);
  }

  /**
   * Convert messages to OpenAI format with history support
   */
  protected getMessagesDefinition(messages: Message[]): OpenAI.Chat.ChatCompletionMessageParam[] {
    // 将AI的参数进行组装，提升历史消息的兼容性
    const supportsFunctionCalling = isSupportFunctionCalling(this.modelConfig.model);
    return messages.map((message) => {
      if (message.role === 'assistant') {
        // 如果消息是助手消息，则需要解析工具调用
        const currMessage = message.message;
        let messageStr = '';
        if (currMessage) {
          try {
            const jsonMessage = JSON.parse(currMessage);
            if (!supportsFunctionCalling) {
              if (Array.isArray(jsonMessage)) {
                jsonMessage.forEach((item) => {
                  if (item.role === 'assistant') {
                    messageStr += `${item.content}${item.tool_calls?.length > 0 ? `<blockquote data-type="tool_call">${JSON.stringify(item.tool_calls)}</blockquote>` : ''}`;
                  }
                  if (item.role === 'tool') {
                    messageStr += `<blockquote data-type="tool_call_result">${JSON.stringify(item)}</blockquote>`
                  }
                  if (item.role === 'user') {
                    messageStr += `${item.content}`
                  }
                });
              } else {
                messageStr += currMessage;
              }
            } else {
              // console.log(jsonMessage);
              if (jsonMessage && jsonMessage.tool_calls && jsonMessage.tool_calls.length > 0) {
                jsonMessage.tool_calls.forEach(toolCall => {
                  console.log(toolCall.function?.arguments);
                });
              }
              return jsonMessage
            }
          } catch (error) {
            messageStr += currMessage;
          }
        } else {
          messageStr += currMessage;
        }
        return {
          role: 'assistant' as const,
          content: messageStr,
          tool_calls: undefined
        }
      }
      if (message.role === 'user') {
        let messageStr = '';
        if (message.message) {
          messageStr += message.message;
        }

        // 处理附件内容
        if (message.attachments) {
          try {
            const attachments: (AttachmentInfo & { content?: any })[] = JSON.parse(message.attachments);
            const multiModalContent: OpenAI.Chat.ChatCompletionContentPart[] = [
              { type: 'text', text: messageStr }
            ];

            // 将附件转换为多模态内容
            for (const attachment of attachments) {
              if (attachment.content && attachment.content.processing_status === 'completed') {
                const contentPart = this.convertAttachmentToContentPart(attachment);
                if (contentPart) {
                  multiModalContent.push(contentPart);
                }
              }
            }

            // 如果有附件内容，返回多模态格式
            if (multiModalContent.length > 1) {
              return {
                role: 'user' as const,
                content: multiModalContent
              };
            }
          } catch (error) {
            logger.warn('处理历史消息附件失败', { messageId: message.id, error: error instanceof Error ? error.message : String(error) });
          }
        }

        return {
          role: 'user' as const,
          content: messageStr
        }
      }
      return null;
    }).flat().filter(Boolean) as OpenAI.Chat.ChatCompletionMessageParam[];
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeTypeFromExtension(extension: string): string {
    const mimeTypeMap: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp'
    };

    return mimeTypeMap[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * 将附件内容转换为多模态内容部分
   */
  private convertAttachmentToContentPart(attachment: AttachmentInfo & { content?: any }): OpenAI.Chat.ChatCompletionContentPart | null {
    if (!attachment.content || attachment.content.processing_status !== 'completed') {
      return null;
    }

    const { content_type, base64_data, raw_content } = attachment.content;
    switch (content_type) {
      case 'image':
        if (base64_data) {
          const mimeType = this.getMimeTypeFromExtension(attachment.extension);
          return {
            type: 'image_url',
            image_url: {
              url: `data:${mimeType};base64,${base64_data}`,
              // detail: 'high'
            }
          };
        }
        break;

      case 'document':
      case 'text':
      case 'code':
        if (raw_content) {
          return {
            type: 'text',
            text: `[文件: ${attachment.originalName}]\n${raw_content}`
          };
        }
        break;

      default:
        logger.warn('不支持的附件内容类型', { contentType: content_type, fileName: attachment.originalName });
        break;
    }

    return null;
  }

  /**
   * Build execution results context for including previous step results
   */
  protected buildExecutionResultsContext(): string {
    if (!this.context?.context.executionResults || this.context.context.executionResults.length === 0) {
      return '';
    }

    const results = this.context.context.executionResults;
    return `## 前序步骤执行结果\n\n${results.map((result, index) => 
      `### 步骤 ${index + 1} 结果:\n${result}\n`
    ).join('\n')}\n---\n\n`;
  }

  /**
   * Get contextual messages with history and execution results
   */
  protected getContextualMessages(currentPrompt: string, includeExecutionResults = true): OpenAI.Chat.ChatCompletionMessageParam[] {
    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [];
    
    // 1. 添加系统提示
    const systemPrompt = this.getSystemPrompt();
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // 2. 添加历史对话消息
    if (this.context?.context.historyMessages) {
      messages.push(...this.getMessagesDefinition(this.context.context.historyMessages));
    }
    
    // 3. 如果需要，添加执行结果上下文
    if (includeExecutionResults) {
      const executionContext = this.buildExecutionResultsContext();
      if (executionContext) {
        messages.push({
          role: 'assistant',
          content: executionContext
        });
      }
    }
    
    // 4. 添加当前步骤/任务提示
    messages.push({
      role: 'user',
      content: currentPrompt
    });
    
    return messages;
  }

  /**
   * Get system prompt for the agent - to be overridden by subclasses
   */
  protected getSystemPrompt(): string {
    return '你是一个智能助手，专门负责协助用户完成各种任务。';
  }
}
