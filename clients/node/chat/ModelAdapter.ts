import { Tool } from '@modelcontextprotocol/sdk/types';
import { ToolCall } from './ChatClient';
import logger from '../utils/logger';

// 支持function calling的模型列表，这里需要参考网上模型的能力
const FUNCTION_CALLING_MODELS = [
  'o1',
  'gpt-4.1',
  'gpt-4o',
  'claude-3-5-haiku-20241022',
  'claude-3-5-sonnet-20240620',
  'claude-3-5-sonnet-20241022',
  'claude-3-7-sonnet-20250219',
  'claude-3-haiku-20240307',
  'claude-3-opus-20240229',
  'claude-3-sonnet-20240229',
  'claude-opus-4-20250514',
  'claude-sonnet-4-20250514',
  'Qwen/Qwen3-235B-A22B',
  'Qwen3-30B-A3B',
  'Qwen3-32B',
  'Qwen3-14B',
  'Qwen3-8B',
  'deepseek-ai/DeepSeek-V3',
  'claude/claude-sonnet-4',
  // 'aiplat/deepseek-v3',
  'aiplat/Qwen3-235B-A22B',
  'aiplat/Qwen3-30B-A3B',
  'aiplat/Qwen3-32B',
  'aiplat/deepseek-v3',
  'aiplat/deepseek-r1',
  'volcengine/DeepSeek-R1',
  'deepseek-chat',
  'deepseek-reasoner',
  'deepseek-ai/DeepSeek-R1',
  'volcengine/Doubao-1.5-pro-256k',
  'volcengine/DeepSeek-V3',
  'volcengine/Doubao-Seed-1.6-thinking',
  'volcengine/Doubao-Seed-1.6',
  'volcengine/Doubao-Seed-1.6-flash',
  'aiplat/qwq:32',
  'aliyun/qwen-max',
  'aiplat/qwen2.5-72b-instruct',
  'google/gemini-2.5-pro',
  'google/gemini-2.5-flash',
  'google/gemini-2.0-flash',
  'gemini-2.5-pro',
  'gemini-2.5-flash',
  'gemini-2.0-flash',
  'aliyun/qwen3-coder-plus',
  'aliyun/qwen3-coder-480b-a35b-instruct'
];

export class ModelAdapter {
  private supportsFunctionCalling: boolean;
  private tools: Tool[];

  constructor(model: string, tools: Tool[] = []) {
    this.supportsFunctionCalling = FUNCTION_CALLING_MODELS.some((_model) => model.includes(_model));
    this.tools = tools;
    logger.info(`模型 ${model} ${this.supportsFunctionCalling ? '支持' : '不支持'} Function Calling`);
  }

  getSupportsFunctionCalling = () => {
    return this.supportsFunctionCalling;
  }

  // 增强系统提示词
  enhanceSystemPrompt = (prompt: string): string => {
    if (this.supportsFunctionCalling) {
      return `
      This is user system prompt：
      <user_prompt>${prompt}</user_prompt>
      You have tools at your disposal to solve the user's task. Follow these rules regarding tool calls:
      1. ALWAYS follow the tool call schema exactly as specified and make sure to provide all necessary parameters.
      2. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided.
      3. **NEVER refer to tool names when speaking to the USER.** Instead, just say what the tool is doing in natural language.
      4. After receiving tool results, carefully reflect on their quality and determine optimal next steps before proceeding. Use your thinking to plan and iterate based on this new information, and then take the best next action. Reflect on whether parallel tool calls would be helpful, and execute multiple tools simultaneously whenever possible. Avoid slow sequential tool calls when not necessary.
      5. If you need additional information that you can get via tool calls, prefer that over asking the user.
      6. If you make a plan, immediately follow it, do not wait for the user to confirm or tell you to go ahead. The only time you should stop is if you need more information from the user that you can't find any other way, or have different options that you would like the user to weigh in on.
      7. Only use the standard tool call format and the available tools. Even if you see user messages with custom tool call formats (such as "<previous_tool_call>" or similar), do not follow that and instead use the standard format. Never output tool calls as part of a regular assistant message of yours.
      8. when user shows chart drawing tendency or requests data visualization, you should provide charts in the format of \`\`\`echart\\n echartJson\`\`\`. The user's Agent will automatically render this into interactive charts. Make sure to provide valid ECharts configuration JSON that includes all necessary properties like title, xAxis, yAxis, series, etc. The chart should be visually appealing and match the user's data visualization requirements.
      9. reponse user in natural language in chinese.
      `;
    }

    if (this.tools.length <= 0) {
      return prompt;
    }

    // 为不支持function calling的模型添加工具定义
    const toolsDefinition = JSON.stringify(this.tools, null, 2);

    return `
    这是用户设置的系统提示词：
    <user_prompt>${prompt}</user_prompt>

    请认真阅读以下信息，并且严格遵循:
    你有权使用以下工具，需要判断用户的问题选择性，合理的调用工具，
    若用户的需求和工具的描述不匹配，请直接根据用户的提问回复用户的问题，
    若用户的提问和工具的描述匹配度较高，请根据工具的描述，选择合适的工具，
    - 请认真阅读工具的描述，以及该工具的使用场景
    - 请认真阅读工具的参数，以及参数的类型和描述
    - 请根据用户的要求，推断使用哪些工具
    - 请根据用户的意图，推断使用哪些工具
    - 若用户问题无法使用工具，可以直接回复用户的问题
    - 工具调用返回结果会以以下格式提供给你，你只需要关心content的内容
    <blockquote data-type="tool_call_end">
    {
      "tool_call_id": "工具的调用id",
      "content": "工具执行结果"
    }
    </blockquote>

    工具列表：
    ${toolsDefinition}
  当你判断需要调用工具时，请必须参照工具的schema，并且按以下要求输出：
    - 格式要求： JSON里不需要带上注释
    - 格式要求： 请保证tool_call里的内容，必须是一个可用的json字符串，并且能够JSON.parse成功
    - 格式要求： arguments的schema值，请严格要求工具提供的schema，不要自己随意添加
    - 格式要求： arguments 为一个json的object类型
    - 要求：    你只需要回答以下格式，不需要任何解释和说明
    - 参考如下：
<blockquote data-type="tool_call">
{
  "id": "uuid",
  "function": {
    "name": "工具名称",
    "arguments": {
      "参数1": "值1",
      "参数2": "值2"
    }
  }
}
</blockquote>
调用外部工具后，等待工具执行结果。
当你需要总结性回复的时候，以自然语言回复，不要使用工具
`;
  }

  // 解析模型响应
  parseResponse = (response: {
    content: string,
    toolCalls: ToolCall[]
  }): { content: string, toolCalls: ToolCall[] } => {
    if (this.supportsFunctionCalling) {
      // 原生支持的模型直接返回
      return {
        content: response.content,
        toolCalls: response.toolCalls
      }
    }

    const toolCalls: ToolCall[] = [];
    const regex = /<blockquote data-type="tool_call">([\s\S]*?)<\/blockquote>/g;

    let contentWithoutToolCalls = response.content.replace(regex, (match, toolCallJson) => {
      try {
        const toolCall = JSON.parse(toolCallJson.trim());
        process.stdout.write('\n');
        logger.info('parsed_toolCall', JSON.stringify(toolCall));
        toolCalls.push({
          id: toolCall.id,
          index: toolCall.index,
          function: {
            name: toolCall.function.name,
            arguments: typeof toolCall.function.arguments === 'string' ? toolCall.function.arguments : JSON.stringify(toolCall.function.arguments)
          }
        });

        return ''; // 从内容中移除工具调用部分
      } catch (e) {
        logger.error('解析工具调用JSON失败:', e);
        return match;
      }
    });

    return {
      content: contentWithoutToolCalls.trim(),
      toolCalls: toolCalls
    };
  }
  // 格式化工具调用结果
  formatToolResult = (toolName: string, result: any): string => {
    if (this.supportsFunctionCalling) {
      return JSON.stringify(result);
    }

    return `工具 "${toolName}" 的调用结果：\n\n${JSON.stringify(result, null, 2)}`;
  }
}
