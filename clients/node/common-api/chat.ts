import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { ElectronAgent } from '../chat/ElectronAgent';
import { ChatClient } from '../chat/ChatClient';
import { getProvider, getModel, Provider, getProviderList, getDefaultModel, Model, getEnabledModelListByProvider } from '../db/provider';
import { saveSession, getSessionsList, deleteSession, Session, getSession, getSessionIndex, deleteAllMessages } from '../db/sessions';
import { getMessagesBySessionId, saveMessage, deleteMessage, createClearContext, deleteMessagesBySessionId, Message, AttachmentInfo } from '../db/messages';
import { AttachmentConverter } from '../attachment/AttachmentConverter';
import logger from '../utils/logger';
import { getUserFromDb } from '../db/user';
import { OpenAI } from 'openai';
export type MessageType =
  'message_start'
  | 'message_delta_content_send'
  | 'message_delta_reasoning_send'
  | 'message_end'
  | 'tool_call_start'
  | 'tool_call_end'
  | 'message_error'
  | 'tool_call_pending';

export const addChatListener = () => {
  ipcMain.handle('create-session', async (event, { provider }: {
    userId: string;
    provider?: string;
  }) => {

    let currentProvider: Provider;
    const user = getUserFromDb();
    if (provider) {
      const _provider = getProvider(provider);
      if (!_provider) {

        return false;
      }
      currentProvider = _provider;
    } else {
      const providerList = getProviderList();
      if (providerList.length === 0) {
        return false;
      }
      currentProvider = providerList[0];
    }

    const modelList = getEnabledModelListByProvider(currentProvider.name);
    // 这里需要改成使用默认模型
    const defaultModel = getDefaultModel(currentProvider.name);
    let currentModel: Model;
    if (defaultModel) {
      currentModel = defaultModel;
    } else {
      currentModel = modelList[0];
    }

    const sessionIndex = getSessionIndex();
    logger.info('sessionIndex', sessionIndex);
    const session = saveSession({
      userId: user.userId,
      name: `默认话题${sessionIndex + 1}`,
      provider: currentProvider.name,
      model: currentModel.model,
      temperature: currentModel.temperature,
      top_p: currentModel.top_p,
      max_tokens: currentModel.max_tokens,
      top_k: currentModel.top_k,
      repetition_penalty: currentModel.repetition_penalty,
      seed: currentModel.seed,
    });
    return session;
  });

  ipcMain.handle('update-session', async (event, { id, updates }: {
    id: number;
    updates: Partial<Session>;
  }) => {
    const updated = saveSession({ id, ...updates });
    return updated;
  });

  ipcMain.handle('get-session-list', async (event, data) => {
    const user = getUserFromDb();
    const sessionList = getSessionsList(user.userId);
    return sessionList;
  });

  ipcMain.handle('delete-session', async (event, { id }: {
    id: number;
  }) => {
    const deleted = deleteSession(id);
    deleteMessagesBySessionId(id);
    return deleted;
  });

  ipcMain.handle('chat', async (event, {
    prompt,
    sessionId,
    mcpIds,
    answerMessageId,
    questionMessageId,
    attachments
  }: {
    prompt: string;
    sessionId: number;
    mcpIds: string;
    questionMessageId?: number;
    answerMessageId?: number;
    attachments?: AttachmentInfo[];
  }) => {

    let messages = getMessagesBySessionId(sessionId);
    let _questionMessageId: number | undefined = undefined;
    let _answerMessageId: number | undefined = undefined;
    let _attachments: AttachmentInfo[] | undefined = attachments;
    const mainWindow = BrowserWindow.getAllWindows()[0];
    const user = getUserFromDb();
    mainWindow.webContents.send('stream_message', {
      type: 'message_will_start',
      data: {
        sessionId: sessionId,
      },
    });
    if (questionMessageId && answerMessageId) {
      mainWindow.webContents.send('stream_message', {
        type: 'message_end',
        data: {
          sessionId: sessionId,
        },
      });
      return false;
    }

    if (answerMessageId) {
      const messageIndex = messages.findIndex(message => message.id === answerMessageId);
      if (messageIndex !== -1) {
        const currQuestionMessage = messages[messageIndex - 1];
        const currAnswerMessage = messages[messageIndex];
        if (currQuestionMessage?.role === 'user' && currAnswerMessage?.role === 'assistant') {
          _questionMessageId = currQuestionMessage.id;
          _answerMessageId = answerMessageId;
          try {
            _attachments = JSON.parse(currQuestionMessage.attachments || '[]');
          } catch (error) {
            logger.error('解析消息附件信息失败:', error, { attachmentsJSONStr: currQuestionMessage.attachments });
          }
        }
        if (!_questionMessageId) {
          mainWindow.webContents.send('stream_message', {
            type: 'message_end',
            data: {
              sessionId: sessionId,
            },
          });
          return false;
        }
      }
      // 截取历史消息，不包含当前回答消息和提问消息
      if (messageIndex - 2 > 0) {
        messages = messages.slice(0, messageIndex - 1);
      } else {
        messages = [];
      }
    }

    if (questionMessageId) {
      // console.log('questionMessageId', questionMessageId);
      const messageIndex = messages.findIndex(message => message.id === questionMessageId);
      // 需要判断下一条消息是否存在
      const nextMessage = messages[messageIndex + 1];
      // console.log('questionMessage', messages[messageIndex]);
      // console.log('nextMessage', nextMessage);
      // console.log('messageIndex', messageIndex);
      if (messageIndex !== -1) {
        _questionMessageId = questionMessageId;
        try {
          _attachments = JSON.parse(messages[messageIndex].attachments || '[]');
        } catch (error) {
          logger.error('解析消息附件信息失败:', error, { attachmentsJSONStr: messages[messageIndex].attachments });
        }
      }
      if (nextMessage && nextMessage.role === 'assistant') {
        _answerMessageId = nextMessage.id;
      }
      if (!_answerMessageId) {
        mainWindow.webContents.send('stream_message', {
          type: 'message_end',
          data: {
            sessionId: sessionId,
          },
        });
        return false;
      }
      // 截取历史消息，不包含当前提问消息
      if (messageIndex - 1 > 0) {
        messages = messages.slice(0, messageIndex);
      } else {
        messages = [];
      }
    }
    // console.log('messages', messages);
    // 截取到最近到上下文被清除的十条消息
    const clearContextMessage = messages.findLast(message => message.role === 'clear-context');
    if (clearContextMessage) {
      messages = messages.slice(messages.indexOf(clearContextMessage) + 1);
    }
    messages = messages.filter(message => message.role !== 'clear-context').slice(-10);
    const session = getSession(sessionId);
    const model = session.model;
    const provider = session.provider;
    const modelConfig = getModel(provider, model);
    const providerConfig = getProvider(provider);
    logger.info('providerConfig', providerConfig);
    logger.info('modelConfig', modelConfig);

    if (!modelConfig || !providerConfig) {
      mainWindow.webContents.send('stream_message', {
        type: 'message_end',
        data: {
          sessionId: sessionId,
        },
      });
      return false;
    }

    // 附件将在ChatClient中处理，这里只传递基本信息

    // logger.info('messages', messages);
    // 创建或者更新一条用户消息
    const messageResult = saveMessage({
      message: prompt,
      avatar: '',
      name: model,
      role: 'user',
      provider,
      model,
      temperature: session.temperature,
      top_p: session.top_p,
      max_tokens: session.max_tokens,
      sessionId: sessionId.toString(),
      userName: user.userId,
      id: _questionMessageId ? _questionMessageId : undefined,
      isDeleted: 0,
      attachments: _attachments ? JSON.stringify(_attachments) : undefined,
    });

    // 发送用户消息创建事件，确保前端同步
    mainWindow.webContents.send('stream_message', {
      type: 'user_message_created',
      data: {
        message: messageResult,
        sessionId: sessionId,
        attachments: _attachments || [],
      },
    });

    const parentMsgId = messageResult.id;

    setTimeout(async () => {
      // console.log('mcpIds', mcpIds);
      const tools = (await ElectronAgent.getInstance().getToolListByMcpIds(mcpIds.split(','))) || [];
      // console.log('tools', tools);
      // 去创建一个助手消息容器，用来存储大模型返回的内容，包含工具调用开始，调用结束，返回参数
      logger.info('modelConfig.logo', modelConfig.logo);
      const assistantMessage = saveMessage({
        message: '',
        avatar: modelConfig.logo,
        name: model,
        role: 'assistant',
        provider,
        model,
        temperature: session.temperature,
        top_p: session.top_p,
        max_tokens: session.max_tokens,
        sessionId: sessionId.toString(),
        userName: user.userId,
        id: _answerMessageId ? _answerMessageId : undefined,
        isDeleted: 0,
      });

      mainWindow.webContents.send('stream_message', {
        type: 'message_start',
        data: {
          sessionId: sessionId,
          message: assistantMessage,
          parentMsgId,
          parentMsg: messageResult,
        }
      });

      const assistantMessageId = assistantMessage.id;

      // console.log('messages', messages);

      let chatClient: ChatClient;

      if (!modelConfig.enabled) {
        mainWindow.webContents.send('stream_message', {
          type: 'message_error',
          data: {
            sessionId: sessionId,
            parentMsgId,
            content: '模型未启用',
          },
        });
        mainWindow.webContents.send('stream_message', {
          type: 'message_end',
          data: {
            sessionId: sessionId,
            parentMsgId,
          },
        });
        return false;
      }

      if (providerConfig.status !== 1) {
        mainWindow.webContents.send('stream_message', {
          type: 'message_error',
          data: {
            sessionId: sessionId,
            parentMsgId,
            content: '服务提供商未启用',
          },
        });
        mainWindow.webContents.send('stream_message', {
          type: 'message_end',
          data: {
            sessionId: sessionId,
            parentMsgId,
          },
        });
        return false;
      }

      if (modelConfig && providerConfig) {
        chatClient = new ChatClient(
          modelConfig,
          providerConfig,
          tools,
          session.system_prompt,
          messages,
          sessionId,
          assistantMessageId,
          session.temperature,
          session.max_tokens,
          session.top_p,
          session.seed,
          session.top_k,
          session.repetition_penalty,
          _questionMessageId,
        );
        await chatClient.initHistoryMessages(messages);
      } else {
        mainWindow.webContents.send('stream_message', {
          type: 'message_end',
          data: {
            sessionId: sessionId,
            parentMsgId,
          },
        });
        return false;
      }
      // 处理附件并创建多模态prompt
      let finalPrompt: string | (string | any)[] = prompt;

      if (_attachments && _attachments.length > 0) {
        try {
          logger.info('开始处理附件为多模态内容', {
            count: _attachments.length,
            files: _attachments.map(f => f.originalName)
          });

          const attachmentConverter = new AttachmentConverter();
          const multiModalContents = await attachmentConverter.convertAttachmentsToMultiModal(_attachments);

          // 创建统一的prompt（文本 + 多模态内容）
          finalPrompt = attachmentConverter.createUnifiedPrompt(prompt, multiModalContents);

          logger.info('附件处理完成', {
            multiModalCount: multiModalContents.length,
            promptType: Array.isArray(finalPrompt) ? 'multimodal' : 'text'
          });

        } catch (error) {
          logger.error('附件处理失败，使用纯文本模式', error);
          // 即使附件处理失败，也继续发送纯文本消息
        }
      }

      await ElectronAgent.getInstance().invoke(finalPrompt, chatClient, mcpIds);
      mainWindow.webContents.send('stream_message', {
        type: 'message_end',
        data: {
          sessionId: sessionId,
          parentMsgId,
        },
      });
    });
    return messageResult;
    // event.sender.send('chat', result);
  });

  ipcMain.handle('clear-context', async (event, { sessionId }: {
    sessionId: number;
  }) => {
    const result = createClearContext(sessionId);
    const mainWindow = BrowserWindow.getAllWindows()[0];
    mainWindow.webContents.send('clear-context', {
      type: 'clear-context',
      data: {
        sessionId: sessionId,
        message: result,
      }
    });
    return result;
  });

  ipcMain.handle('get-message-list', async (event, { sessionId }: {
    sessionId: number;
  }) => {
    const messages = getMessagesBySessionId(sessionId);
    return messages;
  });


  const summarizeMessageTitle = async (messages: Message[], provider: string, model: string) => {
    const message_content = messages.map((message) => message.message).join('\n');
    const currProvider = getProvider(provider);
    if (!currProvider) {
      return false;
    }
    const apiKey = currProvider.currentAPIKey;
    const baseURL = currProvider.apiBaseUrl;
    const openai = new OpenAI({
      baseURL,
      apiKey,
    })
    try {
      const result = await openai.chat.completions.create({
        model,
        messages: [
          {
            role: 'user',
            content: `
            请根据我提供的内容，帮我总结成一个标题，字数不超过20个字, 不要出现任何其他内容，
            - 格式要求
            - 字数不超过二十个字
            - 不要出现其他内容，只需要返回标题即可
            以下我提供的内容
            ${message_content}
          `
          }
        ]
      })
      return result.choices[0].message.content || false;
    } catch (error) {
      logger.error('summarizeMessageTitle error', error);
      return false;
    }
  };


  ipcMain.handle('summarize-message-title', async (event, { sessionId, provider, model }: {
    sessionId: number;
    provider: string;
    model: string;
  }) => {
    const messages = getMessagesBySessionId(sessionId);
    if (messages.length <= 2) {
      const title = await summarizeMessageTitle(messages, provider, model);
      if (title && typeof title === 'string') {
        saveSession({
          id: sessionId,
          name: title,
        });
      }
      return title;
    }
    return false;
  });

  ipcMain.handle('delete-message', async (event, { id }: {
    id: number;
  }) => {
    const deleted = deleteMessage(id);
    return deleted;
  });

  ipcMain.handle('delete-all-messages', async (event, { sessionId }: {
    sessionId: number;
  }) => {
    const deleted = deleteAllMessages(sessionId.toString());
    return deleted;
  });
};
