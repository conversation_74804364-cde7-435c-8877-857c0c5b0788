import React, { useContext, useMemo, useState, useRef, useEffect } from 'react';
import { Dropdown, Collapse, Input, Modal, Tooltip, HighlightText, Button, Checkbox } from '@bdpfe/components';
import classnames from 'classnames';
import { api } from '@/services/api';
import { Session } from '@/services/types';
import { SessionContext } from '@/context/SessionContext';
import Icon from '@/components/MCPIcon';
import SearchInput from '@/components/SearchInput';
import { Empty, MenuProps } from 'antd';
import moreIcon from '@/assets/image/more.svg';
import interact from 'interactjs';
import { PageContext } from '@/context';
import sessionEmpty from '@/assets/image/session-empty.svg';
import searchEmpty from '@/assets/image/search-empty.svg';

export const SessionList: React.FC = () => {
  const { currentSession, setCurrentSession, sessionList, setSessionList, handleCreateSession } = useContext(SessionContext);
  const { sensorsFunc } = useContext(PageContext);
  const [isCollapse, setIsCollapse] = useState(false);
  const [editSessionId, setEditSessionId] = useState<number | null>(null);
  const [editSessionName, setEditSessionName] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [isResizing, setIsResizing] = useState<boolean>(false);
  const [activeKey, setActiveKey] = useState<string[] | null>(null);
  const [isBatchDeleteMode, setIsBatchDeleteMode] = useState<boolean>(false);
  const [selectedSessionIds, setSelectedSessionIds] = useState<Set<number>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);

  const isWindows = window.bdpStudioAPI.isWindows();
  const minWidth = 250;
  const maxWidth = 400;

  // 初始化DOM宽度
  useEffect(() => {
    if (containerRef.current) {
      // 设置默认宽度
      containerRef.current.style.width = '250px';
    }
  }, []);

  // 从localStorage恢复宽度
  useEffect(() => {
    if (containerRef.current) {
      const savedWidth = localStorage.getItem('sessionListWidth');
      if (savedWidth) {
        const parsedWidth = parseInt(savedWidth, 10);
        if (!isNaN(parsedWidth) && parsedWidth >= minWidth && parsedWidth <= maxWidth) {
          containerRef.current.style.width = `${parsedWidth}px`;
        } else {
          containerRef.current.style.width = '250px';
        }
      } else {
        containerRef.current.style.width = '250px';
      }
    }
  }, []);

  // 添加resizable功能
  useEffect(() => {
    if (!containerRef.current || isCollapse) return;

    const element = containerRef.current;

    const interactable = interact(element)
      .resizable({
        edges: { right: true }, // 只允许从右边拖拽
        listeners: {
          start() {
            setIsResizing(true);
          },
          move(event) {
            const target = event.target as HTMLElement;
            let newWidth = event.rect.width;

            // 设置最小和最大宽度限制
            newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));

            // 直接设置DOM宽度
            target.style.width = `${newWidth}px`;

            // 更新actionBar的margin
            const actionBar = document.getElementById('chat-studio-action-bar');
            if (actionBar) {
              actionBar.style.marginRight = `${newWidth - 140}px`;
            }
          },
          end() {
            setIsResizing(false);
            // 保存宽度到localStorage
            const currentWidth = parseInt(element.style.width.replace('px', ''), 10);
            localStorage.setItem('sessionListWidth', currentWidth.toString());
          }
        },
        modifiers: [
          interact.modifiers.restrictSize({
            min: { width: minWidth, height: 100 },
            max: { width: maxWidth, height: 2000 }
          })
        ]
      });

    // 清理函数
    return () => {
      interactable.unset();
    };
  }, [isCollapse, minWidth, maxWidth]);

  const handleDeleteSession = (session: Session) => {
    sensorsFunc('AI_PLAT_delete_session', { sessionId: `${session.id}` })
    Modal.confirm({
      title: '确定要删除对话吗？',
      content: '删除后将无法恢复',
      okText: '删除',
      cancelText: '取消',
      onOk: () => {
        api.deleteSession(session.id).then(res => {
          if (session.id === currentSession?.id) {
            setCurrentSession(null);
          }
          setSessionList(sessionList.filter(item => item.id !== session.id));
        });
      },
    });
  }

  const handleToggleBatchMode = () => {
    setIsBatchDeleteMode(!isBatchDeleteMode);
    setSelectedSessionIds(new Set()); // Clear selections when toggling
  };

  const handleSessionSelect = (sessionId: number, checked: boolean) => {
    const newSelected = new Set(selectedSessionIds);
    if (checked) {
      newSelected.add(sessionId);
    } else {
      newSelected.delete(sessionId);
    }
    setSelectedSessionIds(newSelected);
  };

  const handleBatchDelete = () => {
    if (selectedSessionIds.size === 0) return;

    sensorsFunc('AI_PLAT_batch_delete_sessions', { sessionIds: Array.from(selectedSessionIds).join(',') });
    Modal.confirm({
      title: `确定要删除 ${selectedSessionIds.size} 个对话吗？`,
      content: '删除后将无法恢复',
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        try {
          // Batch delete logic
          const deletePromises = Array.from(selectedSessionIds).map(id =>
            api.deleteSession(id)
          );

          await Promise.all(deletePromises);

          // Update session list
          setSessionList(sessionList.filter(item => !selectedSessionIds.has(item.id)));

          // Clear current session if it was deleted
          if (currentSession && selectedSessionIds.has(currentSession.id)) {
            setCurrentSession(null);
          }

          // Exit batch mode
          setIsBatchDeleteMode(false);
          setSelectedSessionIds(new Set());
        } catch (error) {
          console.error('Batch delete failed:', error);
          // You might want to show an error message to the user here
        }
      },
    });
  };

  const handleCancelBatchDelete = () => {
    setIsBatchDeleteMode(false);
    setSelectedSessionIds(new Set());
  };

  const handleEditSession = (session: Session) => {
    sensorsFunc('AI_PLAT_edit_session', { sessionId: `${session.id}` })
    setEditSessionId(session.id);
    setEditSessionName(session.name);
  }

  const handleRenameSession = (session: Session, name: string) => {
    if (!name.trim() || session.name === name) {
      setEditSessionId(null);
      return;
    }
    api.updateSession(session.id, {
      name,
    }).then(res => {
      setEditSessionId(null);
      setSessionList(sessionList.map(item => {
        if (item.id === session.id) {
          return { ...item, name };
        }
        return item;
      }));
    });
  }

  const handleSelectSession = (session: Session) => {
    sensorsFunc('AI_PLAT_select_session',  { session: JSON.stringify(session)} )
    setCurrentSession(session);
  };
  const cls = (session: Session, textEdit: boolean = false) => {
    return classnames({
      'chat-studio__left_list_item': true,
      'chat-studio__left_list_item--editing': textEdit,
      'chat-studio__left_list_item--active': currentSession?.id === session.id,
    });
  };

  const onPanelChange = (keys: string[]) => {
    setActiveKey(keys);
  };

  const menuItems: MenuProps['items'] = [
    {
      label: '重命名',
      key: 'rename',
    },
    {
      label: '删除',
      key: 'delete',
    },
  ];

  const filterSessionList = useMemo(() => {
    return sessionList.filter(item => item?.name?.includes(searchValue));
  }, [sessionList, searchValue])

  const categoryList = useMemo(() => {
    const filterSessionList = sessionList.filter(item => item?.name?.includes(searchValue));
    const list = [{
      title: '今天',
      key: 'today',
      list: filterSessionList.filter(item => new Date(item.updatedAt).toLocaleDateString() === new Date().toLocaleDateString()),
    },
    {
      title: '昨天',
      key: 'yesterday',
      list: filterSessionList.filter(item => new Date(item.updatedAt).toLocaleDateString() === new Date(new Date().getTime() - 24 * 60 * 60 * 1000).toLocaleDateString()),
    },
    {
      title: '7天内',
      key: '7days',
      list: filterSessionList.filter(item =>
        (new Date(item.updatedAt).getTime() >= new Date().getTime() - 7 * 24 * 60 * 60 * 1000) &&
        (new Date(item.updatedAt).toLocaleDateString() !== new Date().toLocaleDateString()) &&
        (new Date(item.updatedAt).toLocaleDateString() !== new Date(new Date().getTime() - 24 * 60 * 60 * 1000).toLocaleDateString())),
    },
    {
      title: '30天内',
      key: '30days',
      list: filterSessionList.filter(item =>
        (new Date(item.updatedAt).getTime() >= new Date().getTime() - 30 * 24 * 60 * 60 * 1000) &&
        (new Date(item.updatedAt).getTime() < new Date().getTime() - 7 * 24 * 60 * 60 * 1000)),
      },
    ];

    const earlierMonthSessions = filterSessionList.filter(item =>
      (new Date(item.updatedAt).getTime() < new Date().getTime() - 30 * 24 * 60 * 60 * 1000) &&
      (new Date(item.updatedAt).getFullYear() === new Date().getFullYear()));

    if (earlierMonthSessions.length > 0) {
      const months = Array.from(new Set(earlierMonthSessions.map(item => new Date(item.updatedAt).getMonth() + 1))).sort((a, b) => b - a);
      months.forEach(month => {
        const title = `${new Date().getFullYear()}-${String(month).padStart(2, '0')}`;
        list.push({
          title,
          key: title,
          list: earlierMonthSessions.filter(item => new Date(item.updatedAt).getMonth() + 1 === month),
        });
      });
    }

    const earlierSessions = filterSessionList.filter(item => new Date(item.updatedAt).getFullYear() < new Date().getFullYear());
    if (earlierSessions.length > 0) {
      list.push({
        title: '更早',
        key: 'earlier',
        list: earlierSessions,
      });
    }
    return list;
  }, [sessionList, searchValue]);

  const sessionCategories = categoryList.map(item => item.key);

  const sessionTitle = <div className={`chat-studio__left_title ${isWindows ? '' : 'drag'}`}>
    {isCollapse ? null : <div className='chat-studio__left_title--text'>话题列表</div>}
    <div className='chat-studio__left_title--action'>
      <Tooltip title='新建默认助手'>
        <span className='chat-studio__left_title--action-icon' onClick={() => handleCreateSession()}>
          <Icon type='mcp-web-create-session-grey' />
        </span>
      </Tooltip>
      {!isBatchDeleteMode && (
        <Tooltip title='批量删除'>
          <span className='chat-studio__left_title--action-icon' onClick={handleToggleBatchMode}>
            <Icon type='mcp-web-delete-grey' />
          </span>
        </Tooltip>
      )}
      <Tooltip title='收起会话列表'>
        <span className='chat-studio__left_title--action-icon' onClick={() => setIsCollapse(!isCollapse)}>
          <Icon type='mcp-web-collapse-session-grey' />
        </span>
      </Tooltip>
    </div>
  </div>;

  const sessionSearchBar = <div className='chat-studio__left_search'>
    <SearchInput onChange={setSearchValue} />
  </div>;

  const batchActionButtons = isBatchDeleteMode ? (
    <div className='chat-studio__left_batch-actions'>
      <Button
        type='primary'
        danger
        size='small'
        disabled={selectedSessionIds.size === 0}
        onClick={handleBatchDelete}
      >
        确认删除 ({selectedSessionIds.size})
      </Button>
      <Button
        size='small'
        onClick={handleCancelBatchDelete}
      >
        取消
      </Button>
    </div>
  ) : null;
  const sessionRenderList = <div className='chat-studio__left_list'>
    <Collapse expandIcon={() => <span></span>} accordion={false} collapsible='header' ghost expandIconPosition='end' activeKey={activeKey || sessionCategories} onChange={onPanelChange}>
      {categoryList.map(item => item.list.length > 0 && (
        <Collapse.Panel header={<div className='chat-studio__left_list_category'>{item.title}</div>} key={item.key} showArrow={false}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            {item.list.map(item => editSessionId === item.id ? (
              <div style={{ padding: '9px 12px' }} key={item.id} className={cls(item, true)}>
                <Input
                  bordered={false}
                  size="large"
                  style={{ width: '100%', height: '100%' }}
                  value={editSessionName}
                  onChange={(e) => setEditSessionName(e.target.value)}
                  onPressEnter={() => handleRenameSession(item, editSessionName)}
                  onBlur={() => handleRenameSession(item, editSessionName)}
                  autoFocus
                />
              </div>
            ) : (
              <div
                style={{ padding: '9px 12px' }}
                key={item.id}
                onClick={isBatchDeleteMode ? undefined : () => handleSelectSession(item)}
                className={classnames(cls(item), {
                  'chat-studio__left_list_item--batch-mode': isBatchDeleteMode
                })}
              >
                {isBatchDeleteMode && (
                  <Checkbox
                    checked={selectedSessionIds.has(item.id)}
                    onChange={(e) => handleSessionSelect(item.id, e.target.checked)}
                    onClick={(e) => e.stopPropagation()}
                    style={{ marginRight: 8 }}
                  />
                )}
                <div className='chat-studio__left_list_item--text'>
                  <HighlightText text={item.name} keywords={searchValue} />
                </div>
                {!isBatchDeleteMode && (
                  <div className='chat-studio__left_list_item--action' onClick={(e) => e.stopPropagation()}>
                    <Dropdown menu={{
                      items: menuItems,
                      onClick: ({ key }) => {
                        if (key === 'delete') {
                          handleDeleteSession(item);
                        }
                        if (key === 'rename') {
                          handleEditSession(item)
                        }
                      }
                    }} >
                      <img src={moreIcon} />
                    </Dropdown>
                  </div>
                )}
              </div>
            ))}
          </div>
        </Collapse.Panel>
      ))}
    </Collapse>
    {
      sessionList.length === 0 && (
        <div className='chat-studio__left_list_empty'>
          <Empty description='暂无会话' image={sessionEmpty} />
        </div>
      )
    }
    {
      filterSessionList.length === 0 && sessionList.length > 0 && (
        <div className='chat-studio__left_list_empty'>
          <Empty description='暂无搜索结果' image={searchEmpty} />
        </div>
      )
    }
  </div>;

  const containerStyle = {
    width: isCollapse ? 80 : undefined, // 非折叠状态由DOM直接控制宽度
    minWidth: isCollapse ? 'unset' : 250,
    borderRight: isCollapse ? 'none' : '1px solid #E5E5E5',
  }

  if (isCollapse) {
    return (
      <div
        id='chat-studio-message-list'
        className={`chat-studio__left chat-studio__left--collapsed`}
        style={containerStyle}
      >
        {sessionTitle}
      </div>
    )
  }

  return (
    <div
      id='chat-studio-message-list'
      className={`chat-studio__left ${isResizing ? 'resizing' : ''}`}
      style={containerStyle}
      ref={containerRef}
    >
      {sessionTitle}
      {sessionSearchBar}
      {batchActionButtons}
      {sessionRenderList}
    </div>
  );
};
