:root {
  --high-light-text: #3355ff;
}

.chat-studio {
  height: 100%;
  width: 100%;
  display: flex;
}

.chat-studio__left {
  height: 100%;
  width: 250px;
  min-width: 250px;
  flex: none;
  border-radius: 10px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0.5px solid #ebeef9;
  position: relative;

  // 添加右边缘的拖拽区域
  &::after {
    content: '';
    position: absolute;
    right: -4px;
    top: 0;
    bottom: 0;
    width: 2px;
    cursor: ew-resize;
    z-index: 10;
    background: transparent;
    transition: background 0.2s ease;
  }

  // 鼠标悬停时显示拖拽提示
  &:hover::after {
    background: rgba(51, 85, 255, 0.1);
  }

  // 折叠状态下隐藏拖拽条
  &.chat-studio__left--collapsed {
    &::after {
      display: none;
    }

    &:hover::after {
      display: none;
    }
  }

  // 正在拖拽时的样式
  &.resizing {
    user-select: none;

    &::after {
      background: rgba(51, 85, 255, 0.3);
    }
  }
}

.chat-studio__left_title {
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0px;
  margin: 0px 16px;
  border-bottom: 0.5px solid #ebeef9;

  &.drag {
    -webkit-app-region: drag;
  }
}

.chat-studio__left_title--text {
  color: #1b1c1f;
  font-variation-settings: 'opsz' auto;
  font-size: 16px;
  font-weight: 500;
}

.chat-studio__left_title--action {
  width: 68px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  padding: 0px;
  gap: 10px;
}

.chat-studio__left_title--action-icon {
  color: #2c2d33;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  -webkit-app-region: no-drag;

  &:hover {
    color: #3355ff;
    background: #ebf1ff;
  }

  svg {
    font-size: 16px;
  }
}

.chat-studio__left_search {
  padding: 12px 16px 0px 16px;
  height: 40px;
}

.chat-studio__left_batch-actions {
  padding: 8px 16px;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #E5E5E5;
  margin-bottom: 8px;

  .ant-btn {
    flex: 1;
    height: 32px;
    font-size: 14px;
  }
}

.chat-studio__left_list {
  padding: 0px 16px;
  overflow-y: auto;
  height: calc(100% - 88px);
  display: flex;
  flex-direction: column;
  gap: 4px;

  -webkit-scrollbar {
    display: none;
  }

  .chat-studio__left_list_empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .ant-empty-description {
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: #797c8e;
    }
  }

  .ant-collapse>.ant-collapse-item.ant-collapse-no-arrow>.ant-collapse-header {
    padding-top: 14px;
    padding-bottom: 6px;
    padding-left: 12px;
    padding-right: 40px;
  }

  .ant-collapse-ghost>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
    padding: 0;
  }

  .ant-collapse>.ant-collapse-item .ant-collapse-header-collapsible-only .ant-collapse-header-text {
    color: #b8c1d5;
  }

  .ant-collapse-item.ant-collapse-item-active.ant-collapse-no-arrow .anticon.anticon-right {
    transform: rotate(90deg);
  }

  .ant-collapse-item.ant-collapse-no-arrow .anticon.anticon-right {
    margin-left: 10px;
    font-size: 12px;
    color: #8f8f8f;
  }
}

.chat-studio__create_session {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
}

.chat-studio__left_list_item {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
  border: 1px solid transparent;
  transition: all 0.3s ease;
  color: #1b1c1f;

  &:hover {
    border-radius: 8px;
    /* 状态色/进行色底色 */
    background: #ebf1ff;
    box-sizing: border-box;
    border: 1px solid #ccdbff;
    color: #3355ff;
  }

  &--active {
    border-radius: 8px;
    /* 状态色/进行色底色 */
    background: #ebf1ff;
    box-sizing: border-box;
    border: 1px solid #ccdbff;
    color: #3355ff;
  }

  &--editing {
    background: transparent !important;
    border: 1px solid #3355ff !important;
    border-radius: 8px;

    .bdp-input.ant-input {
      font-size: 14px;
      padding: 0px;
    }

    .bdp-input.ant-input:focus {
      font-size: 14px;
      border: none;
      box-shadow: none;
    }
  }

  &--batch-mode {
    cursor: default;

    &:hover {
      background: transparent;
      border: 1px solid transparent;
      color: #1b1c1f;
    }
  }
}

.bdp-input.ant-input-affix-wrapper:focus {
  box-shadow: none;
  border-color: none;
}

&--action {
  display: none;
}

&--text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%; // 180px
  height: 20px;
  line-height: 20px;
  font-size: 14px;
}

&:hover {
  .chat-studio__left_list_item--action {
    display: block;

    img {
      width: 16px;
      height: 16px;
    }
  }
}

.chat-studio__chat_container {
  height: 100%;
  // width: 100%;
  flex: 1;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}

.chat-studio__chat_config {
  /* 自动布局 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  align-self: stretch;
  z-index: 0;

  &.drag {
    -webkit-app-region: drag;
  }
}

.chat-studio__chat_container--empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 100%;

  img {
    width: 180px;
    height: 180px;
  }

  .ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
}

.chat-studio__chat_config--mcp_tool {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 4px;
  z-index: 1;

  border-radius: 8px;
  /* 中性色/内容区底色 */
  background: #f6f7fa;
  box-sizing: border-box;
  /* 中性色/禁用前景色 */
  border: 0.5px solid #c1c5cf;
  cursor: pointer;
  color: #1b1c1f;
  cursor: pointer;
  -webkit-app-region: no-drag;

  &:hover {
    color: #3355ff;
  }
}

.chat-studio__chat_config--separator {
  width: 1px;
  width: 1px;
  height: 12px;
  /* 中性色/禁用前景色 */
  background: #c1c5cf;
}

.chat-studio__chat_config--model_selector {
  /* 自动布局子元素 */
  width: 266px;
  height: 28px;
  /* 自动布局 */
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 4px;
  z-index: 0;
  border-radius: 8px;
  /* 中性色/内容区底色 */
  background: #f6f7fa;
  box-sizing: border-box;
  /* 中性色/禁用前景色 */
  border: 0.5px solid #c1c5cf;
  -webkit-app-region: no-drag;

  svg {
    color: #797c8e;

    &:hover {
      color: #3355ff;
    }
  }
}

.chat-studio__chat_config--model_selector--name {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #1b1c1f;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    svg {
      color: #3355ff;
    }
  }

  img,
  svg {
    margin-left: auto;
  }

  &:hover {
    color: #3355ff;
  }
}

.chat-studio__model_selector--list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  height: 300px;
  overflow-y: auto;
}

.chat-studio__chat_content {
  flex: 1;
  padding: 10px;
  height: calc(100% - 50px);
  width: 100%;
  font-size: 14px;

  display: flex;
  flex-direction: column;
}

.chat-container__message_list {
  flex: 1;
  margin-bottom: 0px;
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;

  -webkit-scrollbar {
    display: none;
  }
}

.chat-studio-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  cursor: col-resize;
  background: transparent;
  border-right: 1px solid transparent;
  display: block;
  width: 20px;
  cursor: col-resize;
  z-index: 10;
  background: transparent;
}

.chat-studio__model-setting {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .ant-slider-track {
    background: #3355ff;
  }

  .ant-slider-rail {
    /* 中性色/页签底色+禁用背景色 */
    background: #ebedf2;
  }

  .ant-slider:hover .ant-slider-rail {
    background: #ebedf2;
  }

  .ant-slider-handle {
    width: 20px;
    height: 20px;
    background: #ffffff;
    box-sizing: border-box;
    margin-top: -7px;
    /* 主色/智慧蓝 */
    border: 6px solid #3355ff;
  }
}

.chat-studio__model-setting_title {
  font-size: 14px;
  color: #1b1c1f;
  display: flex;
  align-items: center;
  gap: 4px;

  svg {
    cursor: pointer;
    color: #797c8e;
  }
}

.chat-studio__model-setting_content {
  display: flex;
  gap: 8px;
  align-items: center;

  .ant-input-number {
    width: 80px;
  }
}

.chat-studio__mcp-setting {
  border-left: 0.5px solid #ebeef9;
  height: 100%;
  width: 300px;
  position: relative;
  // transition: width 0.1s ease;

  // 添加左边缘的拖拽区域
  &::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 0;
    bottom: 0;
    width: 2px;
    cursor: ew-resize;
    z-index: 10;
    background: transparent;
    transition: background 0.2s ease;
  }

  // 鼠标悬停时显示拖拽提示
  &:hover::before {
    background: rgba(51, 85, 255, 0.1);
  }

  // 正在拖拽时的样式
  &.resizing {
    user-select: none;
    // pointer-events: none;

    &::before {
      background: rgba(51, 85, 255, 0.3);
    }
  }
}

.chat-studio__mcp-setting_title {
  font-size: 16px;
  color: #1b1c1f;
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-self: stretch;
  margin: 0 16px;
  height: 48px;
  gap: 4px;
  font-weight: 500;

  box-sizing: border-box;
  /* 中性色/边框线 */
  border-width: 0px 0px 0.5px 0px;
  border-style: solid;
  border-color: #ebeef9;
}

.chat-studio__mcp-setting_title_collapse {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  cursor: pointer;
  color: #2c2d33;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #3355ff;
    background-color: #ebf1ff;
  }
}

.chat-studio__mcp-setting_content {
  display: flex;
  flex-direction: column;
  padding: 0px 16px;
}

.chat-studio__mcp-setting_item {
  padding: 12px 0px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.chat-container__message_input-wrapper {
  position: relative;
}

.chat-container__message_input {
  flex: none;
  padding: 12px;
  min-height: 106px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  display: flex;
  gap: 12px;
  flex-direction: column;
  // transition: all 0.3s ease;

  .bdp-input-textarea {
    overflow-y: auto !important;
    height: auto !important;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
  }

  &:has(.bdp-input-textarea:focus) {
    border: 1px solid #3355ff;
  }

  .ant-input {
    border: none;
    padding: 0;
  }

  .bdp-input-textarea.ant-input:focus {
    border: none;
    box-shadow: none;
  }

  .bdp-input-textarea.ant-input::placeholder {
    font-weight: normal;
    color: #c3c9e5;
  }
}

.chat-container__action_bar--clear {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  /* 中性色/内容区底色 */
  background: #f6f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 10px;

  &:hover {
    background: #ebf1ff;
    color: #3355ff;
  }
}

.chat-container__action_bar {
  position: relative;
  display: flex;

  .ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-btn-primary {
    width: 40px;
    border: none;

    &:hover {
      border: none;
      opacity: 0.8;
    }
  }

  .ant-btn-primary[disabled] {
    background: #e7f0ff;
  }
}

.chat-container__dropdown {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 105px;
}

.chat-container__dropdown-list {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  max-height: 200px;
}

.chat-container__dropdown-item {
  cursor: pointer;
}

.chat-container__dropdown-item--name {
  font-size: 12px;
  font-weight: 500;
}

.chat-container__dropdown-item--desc {
  font-size: 12px;
  color: #8f8f8f;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-container__message_list {
  padding: 0px 20px;
  padding-bottom: 20px;
}

.chat-container__message_list--item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .assistant-message-action {
    padding-left: 40px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
  }

  .user-message-action {
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    font-size: 14px;
  }

  .chat-container__message_list--avatar {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0px;
    gap: 8px;
    font-weight: 600;

    >img {
      width: 30px;
      height: 30px;
      border-radius: 10px;
      margin-right: 4px;
    }
  }
}

.chat-container__message_list--action-icon {
  cursor: pointer;
  color: #797c8e;
  transition: all 0.3s ease;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  &:hover {
    color: #3355ff;
    background: #ebf1ff;
  }
}

.user-message-action .chat-container__message_list--action-icon {
  display: none;
}

.chat-container__message_list--item:hover .chat-container__message_list--action-icon {
  display: flex;
}

.chat-container__message_list--content-wrapper {
  display: flex;
  align-items: center;

  &.user {
    justify-content: flex-end;
    white-space: pre-line;

    .chat-container__message_list--content {
      border-radius: 12px 12px 2px 12px;
      /* 主色/智慧蓝 */
      background: #3355ff;
      padding: 8px 12px;
      max-width: 80%;
      color: #fff;
    }

    .chat-container__message_list--content::selection {
      background: #fff;
      color: #3355ff;
    }
  }

  &.assistant {
    pre {
      code {
        border-radius: 8px;
      }
    }

    .markdown-table {
      width: 100%;
      border-collapse: collapse;
      text-align: left;
      color: #333;
      border: none;

      th,
      td {
        padding: 10px;
        border: 1px solid var(--bdp-line-input-border-color);
        text-align: left;
      }

      tr:nth-child(odd) {
        background-color: #fff;
      }

      tr:nth-child(even) {
        background-color: var(--table-th-bg);
      }

      tr:hover {
        background-color: #fafafa;
      }

      thead th {
        background-color: var(--table-th-bg);
        color: rgba(0, 0, 0, 0.88);
      }
    }

    .ant-collapse {
      border: 0.5px solid #c1c5cf;
      border-radius: 12px;
      width: 100%;
      margin-bottom: 10px;
    }

    .ant-collapse>.ant-collapse-item:last-child {
      border-bottom: none;
    }

    .ant-collapse-item:last-child>.ant-collapse-content {
      border-radius: 0px 0px 12px 12px;
    }

    .ant-collapse>.ant-collapse-item {
      border-bottom: 0.5px solid #c1c5cf;
    }

    .ant-collapse-content {
      border-top: 0.5px solid #c1c5cf;
      padding: 12px;
      display: flex;
      padding: 12px;
      gap: 8px;
      align-self: stretch;
      /* 中性色/内容区底色 */
      background: #f6f7fa;
      /* 中性色/次要文本、图标常规色 */
      color: #797c8e;
      overflow: auto;

      &.ant-collapse-content-hidden {
        display: none;
      }

      &.ant-collapse-content-active {
        max-height: 164px;
      }
    }

    .ant-collapse-content>.ant-collapse-content-box {
      padding: 0px;
    }

    .ant-collapse>.ant-collapse-item>.ant-collapse-header {
      background: #fff;
      /* 自动布局 */
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      align-self: stretch;
      z-index: 1;
      border-radius: 12px;
    }

    // .ant-collapse>.ant-collapse-item {
    //   border: 0.5px solid #C1C5CF;
    // }

    .chat-container__message_list--content {
      border-radius: 0px 12px 12px 12px;
      padding-left: 40px;
      width: 100%;
    }
  }
}

.chat-container__message_list--content {
  overflow: hidden;
  word-break: break-all;
  word-wrap: break-word;
}

.chat-studio__model_list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px 0px;
}

.chat-studio__model_list--item {
  display: flex;
  align-items: center;
  padding: 12px;
  font-size: 14px;
  justify-content: space-between;
  align-self: stretch;
  transition: all 0.3s ease;

  &:hover {
    border-radius: 8px;
    /* 状态色/进行色底色 */
    background: #ebf1ff;
    color: #3355ff;
  }

  &--active {
    border-radius: 8px;
    /* 状态色/进行色底色 */
    background: #ebf1ff;
    color: #3355ff;
  }
}

.chat-studio__model_list--item--content {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
}

.tool-call-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-call-header--separator {
  width: 1px;
  height: 12px;
  background: #c1c5cf;
}

.tool-call-header--status-success {
  color: #28c58e;
}

.tool-call-header--status-error {
  color: #fa5a5a;
}

.tool-call-header--status-pending {
  color: #3355ff;
}

.tool-call-header--status-pending-icon {
  animation: spin 1s linear infinite;
  width: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.tool-call-header--copy {
  margin-left: auto;
  cursor: pointer;
  color: #797c8e;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;

  &:hover {
    border-radius: 4px;
    /* 状态色/进行色底色 */
    background: #ebf1ff;
    color: #3355ff;
  }
}

.tool-call-header--arrow {
  width: 20px;
  height: 20px;
  font-size: 16px;
  color: #797c8e;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;

  &:hover {
    border-radius: 4px;
    /* 状态色/进行色底色 */
    background: #ebf1ff;
    color: #3355ff;
  }
}

.reasoning-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 0px 10px;
  border-left: 2px solid #b8c1d5;
  margin: 8px 0px;
}

.reasoning-content--title {
  font-size: 14px;
  /* 中性色/正文 */
  color: #797c8e;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;

  img {
    width: 20px;
    cursor: pointer;
  }
}

.reasoning-content--content {
  font-size: 12px;
  /* 中性色/正文 */
  color: #797c8e;
  // -webkit-text-stroke: #797C8E 1px;
  /* 浏览器可能不支持 */
}

.reasoning-content--content>*:last-child {
  margin-bottom: 0;
}

.chat-studio__model-setting-drawer {
  -webkit-app-region: no-drag;
}

.html-preview-drawer {
  .ant-drawer-wrapper-body {
    background: #e9ebf2;
  }

  .ant-drawer-body {
    border-top-left-radius: 16px;
    padding: 0;
  }

  .ant-drawer-content-wrapper {
    width: calc(100vw - 72px);
    margin-left: 72px;
    box-shadow: none;
  }

  .ant-drawer-content {
    box-shadow: none;
  }
}