import React, { useContext, useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { Input, Tooltip, Modal, message } from '@bdpfe/components';
import type { TextAreaRef } from 'antd/es/input/TextArea';
import SendLightIcon from '@/assets/image/send-blue.png';
import StopSendGreyIcon from '@/assets/image/stop-send.png';
import Icon from '@/components/MCPIcon';
import AttachmentTags from './AttachmentTags';
import AttachmentPreviewModal from './AttachmentPreviewModal';
import { Session, AttachmentInfo } from '@/services/types';
import { api } from '@/services/api';
import { useMemoizedFn } from 'ahooks';
import interact from 'interactjs'
import { PageContext } from '@/context';
import { SessionContext } from '@/context/SessionContext';

// ChatInput ref接口定义
export interface ChatInputRef {
  addFiles: (files: File[]) => Promise<void>;
  clearFiles: () => void;
  getFiles: () => AttachmentInfo[];
}

interface ChatInputProps {
  sendLoading: boolean;
  session: Session;
  handleDeleteAllMessages: () => void;
}

const TextArea = Input.TextArea;

export const ChatInput = forwardRef<ChatInputRef, ChatInputProps>(({ sendLoading, session, handleDeleteAllMessages }, ref) => {
  const [prompt, setPrompt] = useState<string>('');
  // const [focusServerId, setFocusServerId] = useState<number | null>(null);
  const [dropDownVisible, setDropDownVisible] = useState<boolean>(false);
  const [height, setHeight] = useState(106);
  const [attachmentUploading, setAttachmentUploading] = useState<boolean>(false);
  const [attachedFiles, setAttachedFiles] = useState<AttachmentInfo[]>([]);
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [previewAttachment, setPreviewAttachment] = useState<AttachmentInfo | null>(null);
  const inputRef = useRef<TextAreaRef>(null);
  const inputWrapperRef = useRef<HTMLDivElement>(null);
  const { sensorsFunc } = useContext(PageContext);
  const { currentSession } = useContext(SessionContext);

  // 处理单个文件上传（返回AttachmentInfo或null）
  const handleSingleFileUpload = useMemoizedFn(async (file: File): Promise<AttachmentInfo | null> => {
    // 文件类型验证
    if (!isAllowedFileType(file)) {
      message.error(`不支持的文件类型: "${file.name}"\n支持的类型: 图片、文档(PDF/Office)、代码/文本文件`);
      return null;
    }

    // 文件大小验证 (10MB限制)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      message.error(`文件 "${file.name}" 太大，最大支持10MB`);
      return null;
    }

    // 生成文件名（如果没有名称）
    const fileName = file.name || `uploaded-file-${Date.now()}-${Math.random().toString(36).substring(2, 8)}.${getFileExtensionFromMimeType(file.type)}`;

    try {
      // 将文件转换为 ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const bufferArray = Array.from(uint8Array);

      // 调用上传API
      const result = await api.uploadAttachmentFromClipboard({
        buffer: bufferArray,
        originalName: fileName,
        mimeType: file.type || 'application/octet-stream',
        size: file.size
      });

      if (result.success) {
        // 直接添加到attachedFiles状态
        setAttachedFiles(prev => [...prev, result.data]);
        return result.data;
      } else {
        message.error(result.message || `文件 "${file.name}" 上传失败`);
        return null;
      }
    } catch (error: any) {
      console.error('文件上传错误:', error);
      message.error(`文件 "${file.name}" 上传失败，请重试`);
      return null;
    }
  });

  // 处理拖拽添加的文件
  const handleAddFiles = useMemoizedFn(async (files: File[]) => {
    if (attachmentUploading) {
      message.warning('文件正在上传中，请稍后再试');
      return;
    }

    // 检查当前会话是否为活跃会话
    if (session?.id !== currentSession?.id) {
      message.warning('请在当前活跃的会话中上传文件');
      return;
    }

    // 限制文件数量
    if (files.length > 5) {
      message.error(`一次最多只能上传 5 个文件，当前选择了 ${files.length} 个文件`);
      return;
    }

    // 检查附件总数限制
    if (attachedFiles.length + files.length > 10) {
      message.error(`最多只能附加10个文件，当前已有${attachedFiles.length}个文件`);
      return;
    }

    setAttachmentUploading(true);

    try {
      // 并发处理所有文件
      const uploadPromises = files.map(file => handleSingleFileUpload(file));
      const results = await Promise.all(uploadPromises);

      // 过滤出成功上传的文件
      const successfulUploads = results.filter((result): result is AttachmentInfo => result !== null);
      const failedCount = files.length - successfulUploads.length;

      if (successfulUploads.length > 0) {
        // 显示成功消息
        if (failedCount === 0) {
          message.success(`成功上传 ${successfulUploads.length} 个文件`);
        } else {
          message.warning(`成功上传 ${successfulUploads.length} 个文件，${failedCount} 个文件上传失败`);
        }
      } else {
        message.error('所有文件上传失败，请检查文件格式和大小');
      }
    } catch (error) {
      console.error('批量文件上传错误:', error);
      message.error('文件上传过程中发生错误，请重试');
    } finally {
      setAttachmentUploading(false);
    }
  });

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    addFiles: handleAddFiles,
    clearFiles: () => setAttachedFiles([]),
    getFiles: () => attachedFiles
  }), [attachedFiles, handleAddFiles]);


  const textAreaRef = {
    ref: inputRef
  };

  const getSelectedServerIds = useMemoizedFn(() => {
    try {
      return JSON.parse(localStorage.getItem(`selectedMcps_${session?.id}`) || '[]');
    } catch (error) {
      return [];
    }
  });

  const handleSendMessage = useMemoizedFn(async () => {
    sensorsFunc('AI_PLAT_send_message', { session: JSON.stringify(session), prompt });

    if (sendLoading) {
      api.sendInterruptStream(session?.id);
      return;
    }

    if (!prompt?.trim()) {
      return;
    }

    if (!session) {
      return;
    }

    // 清空输入和附件
    setPrompt('');
    const currentAttachments = [...(attachedFiles || [])];
    setAttachedFiles([]);

    // 发送消息到主进程处理
    try {
      await api.chat(
        prompt,
        session?.id,
        getSelectedServerIds().join(','),
        undefined,
        undefined,
        currentAttachments.length > 0 ? currentAttachments : undefined
      );


    } catch (error) {
      console.error('消息发送失败:', error);
      message.error('消息发送失败，请重试');
    }
  });

  const handleKeyDown = useMemoizedFn((e: KeyboardEvent) => {
    if (e.keyCode === 13 && !e.shiftKey && document.activeElement === document.getElementById(`user-input${session?.id}`) && !dropDownVisible) {
      e.preventDefault();
      inputRef.current.blur();
      handleSendMessage();
    }
  });

  const handleMouseDown = useMemoizedFn((e) => {
    if (!inputWrapperRef.current.contains(e.target)) {
      setDropDownVisible(false);
    }
  });

  const handleResize = (event) => {
    const inputContainer = document.getElementById(`message-input-${session.id}`) as HTMLElement;
    if (inputContainer) {
      inputContainer.style.height = `${event.rect.height}px`;
    }
  };



  useEffect(() => {
    // 窗口焦点监听，防止dialog异常时loading状态无法重置
    const handleWindowFocus = () => {
      // 当窗口重新获得焦点时，如果loading状态持续时间过长，自动重置
      if (attachmentUploading) {
        console.log('检测到窗口重新获得焦点，检查上传状态');
        // 延迟一点时间再检查，给正常的上传流程一些时间
        setTimeout(() => {
          if (attachmentUploading) {
            console.log('重置异常的上传状态');
            setAttachmentUploading(false);
          }
        }, 1000);
      }
    };


    document.addEventListener('paste', handlePaste);
    window.addEventListener('focus', handleWindowFocus);



    return () => {

      document.removeEventListener('paste', handlePaste);
      window.removeEventListener('focus', handleWindowFocus);

    };
  }, [attachmentUploading]);


  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    interact(`#message-input-${session.id}`).resizable({
      edges: { top: true },
      inertia: true,
      modifiers: [
        interact.modifiers.restrictSize({
          min: { height: 106 },
          max: { height: (document.querySelector('.chat-studio__chat_content')?.clientHeight || 0) / 2 }
        } as any)
      ],
    }).on('resizemove', handleResize);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      interact(`#message-input-${session.id}`).unset();
    }
  }, [session?.id]);

  const handleClear = useMemoizedFn(async () => {
    sensorsFunc('AI_PLAT_clear_context', { session: JSON.stringify(session) });
    await api.clearContext(session?.id);
  });

  const handleUploadAttachment = useMemoizedFn(async () => {
    if (attachmentUploading) return;

    if (attachedFiles.length >= 10) {
      message.error('最多只能上传10个附件');
      return;
    }

    setAttachmentUploading(true);

    try {
      const result = await api.uploadAttachment();

      if (result.success) {
        message.success(`附件 "${result.data.originalName}" 上传成功！`);
        // 将文件信息添加到attachedFiles数组
        setAttachedFiles(prev => [...prev, result.data]);
        console.log('上传的附件信息:', result.data);
      } else {
        // 检查是否为用户取消操作
        if (result.canceled) {
          // 用户取消不显示错误消息
          console.log('用户取消文件选择');
        } else {
          message.error(result.message || '附件上传失败');
        }
      }
    } catch (error) {
      console.error('附件上传错误:', error);
      message.error('附件上传失败，请重试');
    } finally {
      setAttachmentUploading(false);
    }
  });

  const handleRemoveAttachment = useMemoizedFn(async (fileId: string) => {
    const fileToRemove = attachedFiles.find(file => file.id === fileId);
    if (!fileToRemove) return;

    try {
      const result = await api.deleteAttachment(fileToRemove.fileName);
      if (result.success) {
        setAttachedFiles(prev => prev.filter(file => file.id !== fileId));
        message.success(`附件 "${fileToRemove.originalName}" 已删除`);
      } else {
        message.error(result.message || '删除附件失败');
      }
    } catch (error) {
      message.error('删除附件失败');
      console.error('删除附件错误:', error);
    }
  });

  const handlePreviewAttachment = useMemoizedFn((attachment: AttachmentInfo) => {
    setPreviewAttachment(attachment);
    setPreviewVisible(true);
  });

  const handleClosePreview = useMemoizedFn(() => {
    setPreviewVisible(false);
    setPreviewAttachment(null);
  });

  const handlePaste = useMemoizedFn(async (e: ClipboardEvent) => {
    // 方案一：检查当前会话是否为活跃会话
    if (session?.id !== currentSession?.id) {
      console.log(`粘贴事件被忽略：非活跃会话 (当前会话: ${session?.id}, 活跃会话: ${currentSession?.id})`);
      return;
    }

    // 方案二：检查当前输入框是否获得焦点
    const activeElement = document.activeElement;
    const currentInput = document.getElementById(`user-input${session?.id}`);
    if (activeElement !== currentInput) {
      console.log(`粘贴事件被忽略：输入框未获得焦点 (会话: ${session?.id})`);
      return;
    }

    console.log(`处理粘贴事件：会话 ${session?.id}`);

    const items = e.clipboardData?.items;
    if (!items) return;

    let hasFile = false;
    let fileCount = 0;

    // 检查是否有文件
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === 'file') {
        hasFile = true;
        fileCount++;
      }
    }

    if (!hasFile) return;

    // 如果正在上传中，阻止新的粘贴操作
    if (attachmentUploading) {
      e.preventDefault();
      message.warning('文件正在上传中，请稍后再试');
      return;
    }

    // 限制同时粘贴的文件数量
    if (fileCount > 5) {
      e.preventDefault();
      message.error('一次最多只能粘贴5个文件');
      return;
    }

    // 检查附件总数限制
    if (attachedFiles.length + fileCount > 10) {
      e.preventDefault();
      message.error(`最多只能附加10个文件，当前已有${attachedFiles.length}个文件`);
      return;
    }

    e.preventDefault();

    // 设置整体上传状态
    setAttachmentUploading(true);

    try {
      // 收集所有文件
      const files: File[] = [];
      const rejectedFiles: string[] = [];

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            // 验证文件类型
            if (isAllowedFileType(file)) {
              files.push(file);
            } else {
              rejectedFiles.push(file.name || '未知文件');
            }
          }
        }
      }

      // 如果有不支持的文件类型，显示提示
      if (rejectedFiles.length > 0) {
        message.error(`不支持的文件类型: ${rejectedFiles.join(', ')}\n支持的类型: 图片、文档(PDF/Office)、代码/文本文件`);
      }

      // 如果没有有效文件，直接返回
      if (files.length === 0) {
        return;
      }

      // 并发处理所有文件
      await Promise.all(files.map(file => handlePasteFile(file)));

    } catch (error) {
      console.error('批量文件粘贴错误:', error);
      message.error('部分文件粘贴失败，请重试');
    } finally {
      // 无论成功失败都要重置状态
      setAttachmentUploading(false);
    }
  });

  const handlePasteFile = useMemoizedFn(async (file: File) => {
    // 文件类型验证
    if (!isAllowedFileType(file)) {
      message.error(`不支持的文件类型: "${file.name}"\n支持的类型: 图片、文档(PDF/Office)、代码/文本文件`);
      throw new Error(`不支持的文件类型: ${file.name}`);
    }

    // 文件大小验证 (50MB限制)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      message.error(`文件 "${file.name}" 太大，最大支持10MB`);
      throw new Error(`文件 "${file.name}" 太大`);
    }

    // 生成文件名（如果没有名称）
    const fileName = file.name || `pasted-image-${Date.now()}-${Math.random().toString(36).substring(2, 8)}.${getFileExtensionFromMimeType(file.type)}`;

    try {
      // 将文件转换为 ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      // 将 ArrayBuffer 转换为 Uint8Array，然后转换为普通数组
      const uint8Array = new Uint8Array(arrayBuffer);
      const bufferArray = Array.from(uint8Array);

      // 调用新的 IPC 方法处理剪贴板文件
      const result = await api.uploadAttachmentFromClipboard({
        buffer: bufferArray,
        originalName: fileName,
        mimeType: file.type || 'application/octet-stream',
        size: file.size
      });

      if (result.success) {
        message.success(`文件 "${result.data.originalName}" 粘贴成功！`);
        setAttachedFiles(prev => [...prev, result.data]);
        console.log('粘贴的文件信息:', result.data);
      } else {
        message.error(result.message || '文件粘贴失败');
      }
    } catch (error) {
      console.error('文件粘贴错误:', error);
      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch')) {
          message.error('网络连接错误，请检查网络后重试');
        } else if (error.message.includes('timeout')) {
          message.error('文件处理超时，请尝试较小的文件');
        } else {
          message.error(`文件粘贴失败：${error.message}`);
        }
      } else {
        message.error('文件粘贴失败，请重试');
      }
      throw error; // 重新抛出错误，让 Promise.all 能捕获到
    }
  });

  const isAllowedFileType = useMemoizedFn((file: File): boolean => {
    const allowedMimeTypes = new Set([
      // 图片类型
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
      // 文本类型
      'text/plain',
      'text/markdown',
      'text/html',
      'text/css',
      'text/javascript',
      'text/xml',
      'application/json',
      'application/xml',
      'application/javascript',
      // PDF
      'application/pdf',
      // Office 文档
      'application/msword', // .doc
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/vnd.ms-excel', // .xls
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-powerpoint', // .ppt
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
    ]);

    // 检查 MIME 类型
    if (allowedMimeTypes.has(file.type)) {
      return true;
    }

    // 如果 MIME 类型为空或不准确，通过文件扩展名检查
    const fileName = file.name.toLowerCase();
    const allowedExtensions = [
      // 图片
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',

      // 基础文本
      '.txt', '.md', '.markdown', '.html', '.htm', '.xml', '.css',

      // 编程语言
      '.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte',
      '.py', '.pyw', '.pyc', '.pyo', '.pyd',
      '.java', '.class', '.jar',
      '.c', '.h', '.cpp', '.hpp', '.cc', '.cxx',
      '.cs', '.vb', '.fs', '.fsx',
      '.php', '.phtml',
      '.rb', '.rbw',
      '.go', '.mod', '.sum',
      '.rs', '.rlib',
      '.swift',
      '.kt', '.kts',
      '.scala', '.sc',
      '.clj', '.cljs', '.cljc',
      '.lua',
      '.perl', '.pl', '.pm',
      '.r', '.rmd',
      '.m', '.mm',
      '.dart',
      '.elm',
      '.ex', '.exs',
      '.jl',
      '.nim',
      '.zig',

      // 配置文件
      '.json', '.yaml', '.yml', '.toml', '.ini', '.conf', '.config',
      '.env', '.gitignore', '.gitattributes',
      '.dockerignore', '.dockerfile',
      '.editorconfig', '.eslintrc', '.prettierrc',
      '.babelrc', '.npmrc', '.yarnrc',

      // 脚本文件
      '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',

      // 标记语言
      '.rst', '.tex', '.latex', '.org', '.adoc',

      // 数据文件
      '.csv', '.tsv', '.log', '.sql',

      // Web相关
      '.scss', '.sass', '.less', '.stylus',
      '.coffee', '.litcoffee',
      '.pug', '.jade', '.ejs', '.hbs', '.handlebars',

      // 移动开发
      '.gradle', '.pro', '.plist',

      // PDF
      '.pdf',

      // Office
      '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'
    ];

    return allowedExtensions.some(ext => fileName.endsWith(ext));
  });

  const getFileExtensionFromMimeType = useMemoizedFn((mimeType: string): string => {
    const mimeToExtension: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/bmp': 'bmp',
      'image/webp': 'webp',
      'text/plain': 'txt',
      'text/markdown': 'md',
      'text/html': 'html',
      'application/pdf': 'pdf',
      'application/msword': 'doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
      'application/vnd.ms-excel': 'xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
      'application/vnd.ms-powerpoint': 'ppt',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx'
    };

    return mimeToExtension[mimeType] || 'bin';
  });

  return (
    <div className='chat-container__message_input-wrapper' ref={inputWrapperRef}>
      <div className='chat-container__message_input' id={`message-input-${session.id}`} onClick={() => inputRef.current?.focus()} >
        <AttachmentTags
          attachments={attachedFiles}
          onRemove={handleRemoveAttachment}
          onPreview={handlePreviewAttachment}
        />
        <TextArea
          style={{ lineHeight: '20px', flex: 1, fontSize: '14px' }}
          autoSize={{ minRows: 2 }}
          placeholder="在这里输入问题，Enter发送，Shift+Enter 换行，Ctrl+V 粘贴文件，或直接拖拽文件上传"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          id={`user-input${session?.id}`}
          {...textAreaRef}
        />
        <div className='chat-container__action_bar'>
          <div className='chat-container__action_bar--clear' onClick={handleUploadAttachment}>
            <Tooltip
              title={
                attachmentUploading
                  ? '上传中...'
                  : (
                    <div>
                      <div style={{ marginBottom: '4px' }}>上传附件</div>
                      <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px' }}>
                        支持格式：
                      </div>
                      <div style={{ fontSize: '11px', color: '#8c8c8c' }}>
                        • 图片：PNG, JPG, JPEG, GIF, BMP, WebP
                      </div>
                      <div style={{ fontSize: '11px', color: '#8c8c8c' }}>
                        • 文档：PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
                      </div>
                      <div style={{ fontSize: '11px', color: '#8c8c8c' }}>
                        • 代码/文本：JS, TS, PY, JAVA, C/C++, GO, RS, 配置文件等
                      </div>
                      <div style={{ fontSize: '11px', color: '#1890ff', marginTop: '4px' }}>
                        💡 提示：也可以直接粘贴文件 (Ctrl+V) 或拖拽文件到聊天区域
                      </div>
                    </div>
                  )
              }
              placement="top"
            >
              <Icon
                type="mcp-web-attachment"
                style={{
                  fontSize: 16,
                  // color: attachmentUploading ? '#1890ff' : '#8c8c8c',
                  // cursor: attachmentUploading ? 'not-allowed' : 'pointer'
                }}
              />
            </Tooltip>
          </div>
          <div className='chat-container__action_bar--clear' onClick={handleClear}>
            <Tooltip title='清除上下文'>
              <Icon type='mcp-web-clear-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <div className='chat-container__action_bar--clear' onClick={handleDeleteAllMessages}>
            <Tooltip title='清空会话'>
              <Icon type='mcp-web-delete-grey' style={{ fontSize: 16 }} />
            </Tooltip>
          </div>
          <Tooltip title={sendLoading ? '停止发送' : '发送'}>
            <img
              style={{ marginLeft: 'auto', cursor: 'pointer', marginRight: sendLoading ? 0 : -7 }}
              onClick={() => handleSendMessage()}
              width={sendLoading ? 28 : 42}
              height={sendLoading ? 28 : 42}
              src={sendLoading ? StopSendGreyIcon : SendLightIcon}
              alt={sendLoading ? 'stop-send-grey' : 'send-light'}
            />
          </Tooltip>
        </div>
      </div>

      <AttachmentPreviewModal
        visible={previewVisible}
        attachment={previewAttachment}
        onClose={handleClosePreview}
      />
    </div>
  );
});
